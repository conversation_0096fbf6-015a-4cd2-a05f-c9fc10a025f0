{"bsonType": "object", "required": ["user_id", "platform_type", "platform_shelf_id", "game_account", "unified_state"], "properties": {"_id": {"description": "货架记录ID"}, "user_id": {"bsonType": "string", "description": "用户ID，关联用户表"}, "platform_type": {"bsonType": "string", "description": "平台类型标识（如：zu<PERSON><PERSON>、uh<PERSON><PERSON>）"}, "platform_shelf_id": {"bsonType": "string", "description": "平台货架ID（平台内部ID）"}, "game_account": {"bsonType": "string", "description": "游戏账号名"}, "game_name": {"bsonType": "string", "description": "游戏名称"}, "game_role_name": {"bsonType": "string", "description": "游戏角色名"}, "shelf_title": {"bsonType": "string", "description": "货架标题"}, "rent_price": {"bsonType": "double", "description": "租赁价格（每小时）"}, "min_rent_time": {"bsonType": "int", "description": "最小租赁时间（小时）"}, "unified_state": {"bsonType": "int", "description": "统一状态：0待租，1出租中，-1下架，-2其他", "default": 0}, "platform_status": {"bsonType": "string", "description": "平台原始状态值（用于调试）"}, "last_sync_time": {"bsonType": "timestamp", "description": "最后更新时间"}, "is_active": {"bsonType": "bool", "description": "是否启用监控", "default": true}, "create_time": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}, "update_time": {"bsonType": "timestamp", "description": "更新时间", "forceDefaultValue": {"$env": "now"}}}}