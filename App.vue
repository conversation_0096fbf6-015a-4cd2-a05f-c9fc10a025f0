<script>
export default {
  onLaunch: function () {
    uni.hideTabBar()
    // console.log('App Launch')
  },
  onShow: function () {
    // console.log('App Show')
  },
  onHide: function () {
    // console.log('App Hide')
  }
}
</script>
<style lang="scss">
/*每个页面公共css */
@import '@/uni_modules/uv-ui-tools/index.scss';

page {
  width: 100%;
  background: $bg-color-page;
  color: $text-color-primary;
  @include responsive-font(base);
  line-height: 1.5;

  // 移动端：为底部导航留出空间
  @include mobile-only {
    height: 100%;
  }

  // PC端：全屏高度
  @include desktop-up {
    height: 100%;
  }
}

/* 全局重置样式 */
* {
  box-sizing: border-box;
}

/* 响应式工具类 */
.mobile-only {
  @include desktop-up {
    display: none !important;
  }
}

.desktop-only {
  @include mobile-only {
    display: none !important;
  }
}

.tablet-up {
  @include mobile-only {
    display: none !important;
  }
}

/* 响应式文本对齐 */
.text-center-mobile {
  @include mobile-only {
    text-align: center;
  }
}

.text-left-desktop {
  @include desktop-up {
    text-align: left;
  }
}

/* 响应式间距工具类 */
.p-responsive {
  @include responsive-spacing(padding, base);
}

.m-responsive {
  @include responsive-spacing(margin, base);
}

.mt-responsive {
  @include responsive-spacing(margin-top, base);
}

.mb-responsive {
  @include responsive-spacing(margin-bottom, base);
}

/* PC端鼠标交互优化 */
@include desktop-up {
  /* 按钮hover效果 */
  .bun-hover {
    &:hover {
      transform: translateY(-1px);
      box-shadow: $shadow-base;
    }
  }

  /* 输入框hover效果 */
  .uv-input {
    &:hover {
      border-color: $primary-color !important;
    }
  }

  /* 卡片hover效果 */
  .card-hover {
    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }
  }

  /* 链接hover效果 */
  .link-hover {
    &:hover {
      color: $primary-color-dark;
      text-decoration: underline;
    }
  }

  /* 禁用选择文本（对于纯交互元素） */
  .no-select {
    user-select: none;
  }

  /* 滚动条样式优化 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: $bg-color-page;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: $border-color-base;
    border-radius: 4px;

    &:hover {
      background: $text-color-secondary;
    }
  }
}


/* 通用工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-1 { flex: 1; }

.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-base { margin-bottom: $spacing-base; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-base { margin-top: $spacing-base; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-base { padding: $spacing-base; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }
</style>
