{"bsonType": "object", "required": ["user_id", "platform_type", "action", "status"], "properties": {"_id": {"description": "日志记录ID"}, "user_id": {"bsonType": "string", "description": "用户ID，关联用户表"}, "platform_type": {"bsonType": "string", "description": "平台类型标识（如：zu<PERSON><PERSON>、uh<PERSON><PERSON>）"}, "platform_shelf_id": {"bsonType": "string", "description": "平台货架ID"}, "game_account": {"bsonType": "string", "description": "游戏账号名"}, "action": {"bsonType": "string", "description": "操作类型：login登录，sync更新，on_shelf上架，off_shelf下架"}, "status": {"bsonType": "int", "description": "操作状态：0失败，1成功"}, "message": {"bsonType": "string", "description": "操作结果消息"}, "error_code": {"bsonType": "string", "description": "错误代码"}, "request_data": {"bsonType": "object", "description": "请求数据（调试用）"}, "response_data": {"bsonType": "object", "description": "响应数据（调试用）"}, "execution_time": {"bsonType": "int", "description": "执行耗时（毫秒）"}, "trigger_type": {"bsonType": "string", "description": "触发类型：auto自动，manual手动"}, "create_time": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}}}