'use strict'
const axios = require('axios')

/**
 * 告警系统常量定义
 */
const ALERT_CONSTANTS = {
  COOLDOWN_PERIOD: 5 * 60 * 1000,    // 告警冷却期：5分钟
  REQUEST_TIMEOUT: 10000,            // 请求超时时间：10秒
  STACK_TRACE_LIMIT: 500,            // 堆栈信息截取长度
  SERVER_CHAN_API_URL: 'https://sctapi.ftqq.com'  // Server酱API地址
}

/**
 * 告警级别定义
 */
const ALERT_LEVELS = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
}

/**
 * 异常告警管理器
 *
 * 提供统一的系统告警服务，支持多种告警渠道和频率控制
 * 主要功能：
 * - Server酱告警推送
 * - 告警频率限制，避免告警轰炸
 * - 告警内容格式化和分级
 * - 告警历史记录管理
 */
class AlertManager {
  constructor(config = {}) {
    this.config = config
    this.serverChanKey = config.serverChanKey || ''

    // 告警频率限制机制（避免告警轰炸）
    this.alertHistory = new Map()
    this.alertCooldown = ALERT_CONSTANTS.COOLDOWN_PERIOD
  }

  /**
   * 发送告警
   *
   * @param {Object} alertData - 告警数据对象
   * @param {string} alertData.title - 告警标题
   * @param {string} alertData.message - 告警内容
   * @param {string} alertData.level - 告警级别: info, warning, error, critical
   * @param {string} alertData.source - 告警来源
   * @param {Object} alertData.extra - 额外数据
   */
  async sendAlert(alertData) {
    try {
      const {
        title,
        message,
        level = ALERT_LEVELS.ERROR,
        source = 'system',
        extra = {}
      } = alertData

      // {{ AURA-X: Modify - 使用统一的告警处理流程. Approval: 寸止(ID:1735373600). }}

      // 步骤1：检查告警频率限制
      const alertKey = `${source}-${title}`
      if (this._shouldSkipAlert(alertKey)) {
        console.log(`告警被频率限制跳过: ${alertKey}`)
        return
      }

      // 步骤2：记录告警历史
      this.alertHistory.set(alertKey, Date.now())

      // 步骤3：构建格式化的告警内容
      const alertContent = this._buildAlertContent({
        title,
        message,
        level,
        source,
        extra,
        timestamp: new Date().toLocaleString('zh-CN')
      })

      // 步骤4：发送告警到各个渠道
      await this._sendToChannels(alertContent)

      console.log(`告警发送完成: ${title}`)
    } catch (error) {
      console.error('发送告警失败:', error)
    }
  }

  /**
   * 检查是否应该跳过告警（频率限制）
   *
   * @param {string} alertKey - 告警键
   * @returns {boolean} 是否跳过
   */
  _shouldSkipAlert(alertKey) {
    // {{ AURA-X: Modify - 重命名为私有方法，提高封装性. Approval: 寸止(ID:1735373600). }}
    const lastAlertTime = this.alertHistory.get(alertKey)
    if (!lastAlertTime) {
      return false
    }

    return (Date.now() - lastAlertTime) < this.alertCooldown
  }

  /**
   * 构建格式化的告警内容
   *
   * @param {Object} data - 告警数据
   * @returns {Object} 格式化的告警内容
   */
  _buildAlertContent(data) {
    // {{ AURA-X: Modify - 重命名为私有方法，使用常量定义. Approval: 寸止(ID:1735373600). }}
    const levelEmoji = {
      [ALERT_LEVELS.INFO]: 'ℹ️',
      [ALERT_LEVELS.WARNING]: '⚠️',
      [ALERT_LEVELS.ERROR]: '❌',
      [ALERT_LEVELS.CRITICAL]: '🚨'
    }

    return {
      title: `${levelEmoji[data.level] || '❌'} ${data.title}`,
      content: `**告警时间**: ${data.timestamp}
**告警级别**: ${data.level.toUpperCase()}
**告警来源**: ${data.source}
**告警内容**: ${data.message}

${data.extra && Object.keys(data.extra).length > 0 ?
  `**详细信息**:
${Object.entries(data.extra).map(([k, v]) => `- ${k}: ${v}`).join('\n')}` : ''}`
    }
  }

  /**
   * 发送告警到各个渠道
   *
   * @param {Object} alertContent - 格式化的告警内容
   */
  async _sendToChannels(alertContent) {
    // {{ AURA-X: Add - 提取告警渠道发送逻辑，便于扩展. Approval: 寸止(ID:1735373600). }}

    // 发送到Server酱
    if (this.serverChanKey) {
      await this._sendServerChan(alertContent)
    } else {
      console.warn('Server酱密钥未配置，跳过告警发送')
    }

    // 未来可以在这里添加其他告警渠道
    // 如：钉钉、企业微信、邮件等
  }

  /**
   * 通过Server酱发送告警（私有方法）
   *
   * @param {Object} alertContent - 告警内容
   */
  async _sendServerChan(alertContent) {
    try {
      // {{ AURA-X: Modify - 使用常量定义的API地址和超时时间. Approval: 寸止(ID:1735373600). }}
      const apiUrl = `${ALERT_CONSTANTS.SERVER_CHAN_API_URL}/${this.serverChanKey}.send`

      await axios.post(apiUrl, {
        title: alertContent.title,
        desp: alertContent.content
      }, {
        timeout: ALERT_CONSTANTS.REQUEST_TIMEOUT
      })

      console.log('Server酱告警发送成功')
    } catch (error) {
      console.error('Server酱告警发送失败:', error.message)
    }
  }



  /**
   * 发送系统异常告警
   *
   * @param {Error} error - 异常对象
   * @param {string} source - 异常来源
   * @param {Object} context - 上下文信息
   */
  async alertSystemError(error, source = 'system', context = {}) {
    // {{ AURA-X: Modify - 使用常量定义和统一的告警级别. Approval: 寸止(ID:1735373600). }}
    await this.sendAlert({
      title: '系统异常告警',
      message: error.message,
      level: ALERT_LEVELS.ERROR,
      source,
      extra: {
        stack: error.stack?.substring(0, ALERT_CONSTANTS.STACK_TRACE_LIMIT) + '...',
        ...context
      }
    })
  }
}

module.exports = AlertManager
