{"openapi": "3.0.1", "info": {"title": "U号租", "description": "U号租官方Api", "version": "1.0.0"}, "tags": [], "paths": {"/tool/user/access-token": {"post": {"summary": "登录（json）", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\r\n    \"from\" : \"\", //来自哪里（蓝天）\r\n    \"password\" : \"2a3dac6cb4059b134251c073fcb5dd88\", // MD5密码\r\n    \"userName\" : \"17602911442\" // 用户名\r\n}\r\n"}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"object": {"type": "string"}, "responseCode": {"type": "string"}, "responseMsg": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["object", "responseCode", "responseMsg", "success"]}, "example": {"object": "s+LRIZFz54HmEeGctfLQAQ==", "responseCode": "0000", "responseMsg": "success", "success": true}}}, "headers": {}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/tool/goods/list/all": {"post": {"summary": "获取货架列表（json）", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "AccessToken", "in": "header", "description": "", "required": true, "example": "s+LRIZFz54HmEeGctfLQAQ==", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {}}, "example": "{\r\n   \"gameId\" : \"\",\r\n   \"goodsStatus\" : 4, // 货架状态：不传 全部 3待租 4下架\r\n   \"rentStatus\" : 1 // 租赁状态：不传 全部 1待租 0出租中，goodsStatus 为3时需要传\r\n}\r\n"}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"object": {"type": "array", "items": {"type": "object", "properties": {"carrierName": {"type": "string"}, "gameAccount": {"type": "string"}, "gameId": {"type": "string"}, "gameName": {"type": "string"}, "gameRoleName": {"type": "string"}, "goodsId": {"type": "integer"}, "goodsStatus": {"type": "integer"}, "goodsTitle": {"type": "string"}, "minRentTime": {"type": "integer"}, "rentStatus": {"type": "integer"}, "rentalByHour": {"type": "integer"}}}}, "responseCode": {"type": "string"}, "responseMsg": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["object", "responseCode", "responseMsg", "success"]}, "example": {"object": [{"carrierName": "", "gameAccount": "lilonghaoshuai6", "gameId": "578080", "gameName": "绝地求生", "gameRoleName": "UE0006", "goodsId": **********, "goodsStatus": 4, "goodsTitle": "3600+皮⭐23神器·满内购丨瀚娜·⑩火麒麟·满级圣诞AK·满女团丨最新装备⭐男女通用", "minRentTime": 2, "rentStatus": 1, "rentalByHour": 900}], "responseCode": "0000", "responseMsg": "success", "success": true}}}, "headers": {}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/tool/goods/status/update": {"post": {"summary": "上下架（json）", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "AccessToken", "in": "header", "description": "", "required": true, "example": "s+LRIZFz54HmEeGctfLQAQ==", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": "{\r\n   \"goodsId\" : \"**********\", // 货架号\r\n   \"status\" : 3 // 3上架 4下架\r\n}\r\n"}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"object": {"type": "boolean"}, "responseCode": {"type": "string"}, "responseMsg": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["object", "responseCode", "responseMsg", "success"]}, "example": {"object": true, "responseCode": "0000", "responseMsg": "success", "success": true}}}, "headers": {}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [], "security": []}