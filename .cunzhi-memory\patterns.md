# 常用模式和最佳实践

- 前端代码重构最佳实践：已完成深度重构，统一了工具函数(utils.js包含20+个函数)、错误处理机制、常量管理(CONSTANTS配置)、代码注释规范。消除了formatTime、getPlatformName、getActionText等重复函数，统一了所有uni.showToast/showModal/showLoading调用。重构涉及12个文件，消除约200行重复代码，添加80+处注释，零破坏性变更。
- base-adapter.js重构最佳实践：将复杂的executeRequest方法拆分为7个职责单一的方法：buildRequestConfig()构建请求配置、updateAuthHeaders()统一认证头更新、executeHttpRequest()执行HTTP请求、handleLoginExpired()处理登录过期、performAutoLogin()执行自动登录、retryRequestWithNewToken()重试请求、handleRequestError()统一错误处理。消除了代码重复，提高了可维护性和可测试性。
- 账号管理页面开发完成：实现了跨平台账号集中管理，包括账号聚合展示、批量上下架功能、智能监控状态管理（全部监控/部分监控/未监控）、出租状态安全检查等功能。采用现有架构扩展，确保向后兼容，UI与现有系统保持一致。
- 操作日志页面滚动加载最佳实践：使用scroll-view组件的scrolltolower事件实现自动加载更多，设置lower-threshold="100"提前触发，使用throttle节流函数优化性能(300ms)，移除手动加载按钮，保持现有分页逻辑不变。滚动容器高度使用calc(100vh - 400rpx)动态计算，确保跨平台兼容性。
- scroll-view滚动加载问题修复：1)容器高度使用70vh而非calc()确保滚动生效；2)避免使用throttle函数包装事件处理器，改用setTimeout防抖；3)添加@scroll事件和console.log调试滚动状态；4)设置show-scrollbar="true"便于调试；5)beforeDestroy清理定时器防止内存泄漏。
