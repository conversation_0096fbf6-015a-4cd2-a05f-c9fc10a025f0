'use strict'

const PlatformAdapterFactory = require('./platform-adapter-factory')
const DatabaseManager = require('./database-manager')
const Logger = require('./logger')

/**
 * 定时任务执行器
 * 
 * 负责执行具体的定时上下架任务
 * 主要功能：
 * 1. 执行账号级别的批量上下架
 * 2. 执行单个货架的上下架
 * 3. 监控状态联动管理
 * 4. 执行结果记录和错误处理
 */
class ScheduledTaskExecutor {
  constructor() {
    this.db = uniCloud.database()
    this.dbCmd = this.db.command
    this.dbManager = new DatabaseManager()
    this.logger = new Logger()
    
    // 状态常量
    this.SHELF_STATES = {
      AVAILABLE: 0,    // 待租
      RENTED: 1,       // 出租中
      OFFLINE: -1      // 下架
    }
  }

  /**
   * 执行定时任务
   * @param {Object} task 任务对象
   * @returns {Object} 执行结果
   */
  async executeTask(task) {
    try {
      console.log(`开始执行定时任务: ${task._id}, 类型: ${task.task_type}, 目标: ${task.target_type}`)
      
      let result
      
      if (task.target_type === 'account') {
        // 执行账号级别任务
        result = await this._executeAccountTask(task)
      } else if (task.target_type === 'shelf') {
        // 执行货架级别任务
        result = await this._executeShelfTask(task)
      } else {
        throw new Error(`不支持的目标类型: ${task.target_type}`)
      }
      
      // 记录操作日志
      await this._logTaskExecution(task, result)
      
      return result
      
    } catch (error) {
      console.error(`执行定时任务失败: ${task._id}`, error)
      
      const result = {
        success: false,
        error_message: error.message,
        success_count: 0,
        failed_count: 1,
        affected_shelves: []
      }
      
      // 记录错误日志
      await this._logTaskExecution(task, result)
      
      return result
    }
  }

  /**
   * 执行账号级别任务
   * @private
   */
  async _executeAccountTask(task) {
    const { user_id, task_type, target_id: gameAccount, auto_disable_monitor } = task
    
    // 获取该账号的所有货架
    const shelves = await this._getAccountShelves(user_id, gameAccount)
    
    if (shelves.length === 0) {
      return {
        success: true,
        message: '该账号暂无货架数据',
        success_count: 0,
        failed_count: 0,
        affected_shelves: []
      }
    }
    
    // 获取平台配置
    const platformConfigs = await this.dbManager.getPlatformConfigs(user_id)
    const platformAdapters = {}
    
    for (const config of platformConfigs) {
      if (config.status === 1) { // 只处理启用的平台
        platformAdapters[config.platform_type] = PlatformAdapterFactory.create(config.platform_type, config)
      }
    }
    
    // 按平台分组执行
    const results = []
    let successCount = 0
    let failedCount = 0
    
    for (const shelf of shelves) {
      const adapter = platformAdapters[shelf.platform_type]
      if (!adapter) {
        results.push({
          shelf_id: shelf._id,
          platform_type: shelf.platform_type,
          success: false,
          message: '平台配置未启用或不存在'
        })
        failedCount++
        continue
      }
      
      try {
        // 执行上下架操作
        let operationResult
        let newState
        let action
        
        if (task_type === 'on_shelf') {
          operationResult = await adapter.onShelf(shelf.platform_shelf_id)
          newState = this.SHELF_STATES.AVAILABLE
          action = 'on_shelf'
        } else {
          operationResult = await adapter.offShelf(shelf.platform_shelf_id)
          newState = this.SHELF_STATES.OFFLINE
          action = 'off_shelf'
        }
        
        // 更新本地数据库状态
        await this._updateShelfStatus(shelf._id, newState)
        
        // 处理监控状态
        if (task_type === 'off_shelf' && auto_disable_monitor) {
          await this._updateShelfMonitorStatus(shelf._id, false)
        }
        
        results.push({
          shelf_id: shelf._id,
          platform_type: shelf.platform_type,
          success: true,
          message: `${action === 'on_shelf' ? '上架' : '下架'}成功`
        })
        
        successCount++
        
        // 记录操作日志
        await this.logger.log({
          user_id: user_id,
          platform_type: shelf.platform_type,
          platform_shelf_id: shelf.platform_shelf_id,
          game_account: gameAccount,
          action: action,
          status: 1,
          message: `定时任务${action === 'on_shelf' ? '上架' : '下架'}成功`,
          trigger_type: 'scheduled'
        })
        
      } catch (error) {
        console.error(`货架 ${shelf._id} 操作失败:`, error)
        
        results.push({
          shelf_id: shelf._id,
          platform_type: shelf.platform_type,
          success: false,
          message: error.message
        })
        
        failedCount++
        
        // 记录错误日志
        await this.logger.log({
          user_id: user_id,
          platform_type: shelf.platform_type,
          platform_shelf_id: shelf.platform_shelf_id,
          game_account: gameAccount,
          action: task_type,
          status: 0,
          message: `定时任务执行失败: ${error.message}`,
          trigger_type: 'scheduled'
        })
      }
    }
    
    return {
      success: successCount > 0,
      message: `执行完成，成功${successCount}个，失败${failedCount}个`,
      success_count: successCount,
      failed_count: failedCount,
      affected_shelves: results
    }
  }

  /**
   * 执行货架级别任务
   * @private
   */
  async _executeShelfTask(task) {
    const { user_id, task_type, target_id: shelfId, platform_type, auto_disable_monitor } = task
    
    // 获取货架信息
    const shelf = await this._getShelfById(shelfId)
    if (!shelf) {
      throw new Error('货架不存在')
    }
    
    // 获取平台配置
    const platformConfigs = await this.dbManager.getPlatformConfigs(user_id, platform_type)
    if (platformConfigs.length === 0 || platformConfigs[0].status !== 1) {
      throw new Error('平台配置不存在或未启用')
    }
    
    const adapter = PlatformAdapterFactory.create(platform_type, platformConfigs[0])
    
    try {
      // 执行上下架操作
      let operationResult
      let newState
      let action
      
      if (task_type === 'on_shelf') {
        operationResult = await adapter.onShelf(shelf.platform_shelf_id)
        newState = this.SHELF_STATES.AVAILABLE
        action = 'on_shelf'
      } else {
        operationResult = await adapter.offShelf(shelf.platform_shelf_id)
        newState = this.SHELF_STATES.OFFLINE
        action = 'off_shelf'
      }
      
      // 更新本地数据库状态
      await this._updateShelfStatus(shelfId, newState)
      
      // 处理监控状态
      if (task_type === 'off_shelf' && auto_disable_monitor) {
        await this._updateShelfMonitorStatus(shelfId, false)
      }
      
      // 记录操作日志
      await this.logger.log({
        user_id: user_id,
        platform_type: platform_type,
        platform_shelf_id: shelf.platform_shelf_id,
        game_account: shelf.game_account,
        action: action,
        status: 1,
        message: `定时任务${action === 'on_shelf' ? '上架' : '下架'}成功`,
        trigger_type: 'scheduled'
      })
      
      return {
        success: true,
        message: `${action === 'on_shelf' ? '上架' : '下架'}成功`,
        success_count: 1,
        failed_count: 0,
        affected_shelves: [{
          shelf_id: shelfId,
          platform_type: platform_type,
          success: true,
          message: `${action === 'on_shelf' ? '上架' : '下架'}成功`
        }]
      }
      
    } catch (error) {
      // 记录错误日志
      await this.logger.log({
        user_id: user_id,
        platform_type: platform_type,
        platform_shelf_id: shelf.platform_shelf_id,
        game_account: shelf.game_account,
        action: task_type,
        status: 0,
        message: `定时任务执行失败: ${error.message}`,
        trigger_type: 'scheduled'
      })
      
      throw error
    }
  }

  /**
   * 获取账号的所有货架
   * @private
   */
  async _getAccountShelves(userId, gameAccount) {
    const { data } = await this.db.collection('account-shelves')
      .where({
        user_id: userId,
        game_account: gameAccount,
        is_active: true // 只处理启用监控的货架
      })
      .get()
    
    return data
  }

  /**
   * 根据ID获取货架信息
   * @private
   */
  async _getShelfById(shelfId) {
    const { data } = await this.db.collection('account-shelves')
      .doc(shelfId)
      .get()
    
    return data.length > 0 ? data[0] : null
  }

  /**
   * 更新货架状态
   * @private
   */
  async _updateShelfStatus(shelfId, newState) {
    await this.db.collection('account-shelves')
      .doc(shelfId)
      .update({
        unified_state: newState,
        last_sync_time: Date.now(),
        update_time: Date.now()
      })
  }

  /**
   * 更新货架监控状态
   * @private
   */
  async _updateShelfMonitorStatus(shelfId, isActive) {
    await this.db.collection('account-shelves')
      .doc(shelfId)
      .update({
        is_active: isActive,
        update_time: Date.now()
      })
  }

  /**
   * 记录任务执行日志
   * @private
   */
  async _logTaskExecution(task, result) {
    try {
      await this.logger.log({
        user_id: task.user_id,
        platform_type: task.platform_type || 'system',
        game_account: task.target_type === 'account' ? task.target_id : '',
        action: `scheduled_${task.task_type}`,
        status: result.success ? 1 : 0,
        message: `定时任务执行${result.success ? '成功' : '失败'}: ${result.message || result.error_message}`,
        trigger_type: 'scheduled'
      })
    } catch (error) {
      console.error('记录任务执行日志失败:', error)
    }
  }
}

module.exports = ScheduledTaskExecutor
