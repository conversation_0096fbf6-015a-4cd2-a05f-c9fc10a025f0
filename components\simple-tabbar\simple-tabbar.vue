<template>
  <view class="simple-tabbar">
    <view
      v-for="(item, index) in tabs"
      :key="index"
      class="tab-item"
      :class="{ active: current === index }"
      @click="switchTab(index)"
    >
      <view class="tab-icon">
        <uv-icon
          :name="item.icon"
          :color="current === index ? '#3c9cff' : '#7A7E83'"
          size="44rpx"
        ></uv-icon>
      </view>
      <view
        class="tab-text"
        :style="{ color: current === index ? '#3c9cff' : '#7A7E83' }"
      >
        {{ item.text }}
      </view>
    </view>
  </view>
</template>

<script>
/**
 * 简化版底部导航栏组件
 * {{ AURA-X: Modify - 添加详细注释，改善组件结构. Approval: 寸止(ID:1735374000). }}
 */
export default {
  name: 'SimpleTabbar',
  props: {
    // 当前激活的tab索引
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 导航栏配置数据 (与pages.json中的tabbar顺序保持一致)
      // {{ AURA-X: Modify - 添加账号管理页面到tabBar导航. Approval: 寸止(ID:**********). }}
      tabs: [
        { text: '监控', icon: 'eye', path: 'pages/shelf-monitor/index' },
        { text: '货架', icon: 'grid', path: 'pages/shelf-list/index' },
        { text: '账号', icon: 'account', path: 'pages/account-management/index' },
        { text: '配置', icon: 'setting', path: 'pages/platform-config/index' },
        { text: '日志', icon: 'file-text', path: 'pages/operation-logs/index' }
      ]
    }
  },
  methods: {
    /**
     * 切换标签页
     * @param {number} index 目标tab索引
     */
    switchTab(index) {
      // 避免重复点击当前tab
      if (this.current === index) return

      const tab = this.tabs[index]

      // 向父组件发送切换事件
      this.$emit('change', { index, path: tab.path })

      // 执行页面跳转，优先使用switchTab，失败时降级为navigateTo
      uni.switchTab({
        url: `/${tab.path}`,
        fail: () => {
          uni.navigateTo({ url: `/${tab.path}` })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.simple-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background-color: $bg-color-container;
  border-top: 1rpx solid $border-color-light;
  display: flex;
  z-index: 1000;

  // 只在移动端显示
  @include desktop-up {
    display: none;
  }

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8rpx 0;
    transition: all $transition-fast;

    &:active {
      background-color: $bg-color-hover;
    }

    .tab-icon {
      margin-bottom: 4rpx;
    }

    .tab-text {
      @include responsive-font(xs);
      color: inherit;
    }
  }
}
</style>
