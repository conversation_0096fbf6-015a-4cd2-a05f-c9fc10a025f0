'use strict'

// {{ AURA-X: Modify - 移除直接数据库操作，统一使用DatabaseManager. Approval: 寸止(ID:**********). }}
const db = uniCloud.database() // 仅用于用户token验证
const dbCmd = db.command

// {{ AURA-X: Modify - 使用公共模块替代重复代码. Approval: 寸止(ID:1735372800). }}
// {{ AURA-X: Add - 集成定时任务管理功能. Approval: 寸止(ID:**********). }}
const { PlatformAdapterFactory, StateManager, Logger, DatabaseManager, ScheduledTaskManager } = require('shelf-core')

// {{ AURA-X: Modify - 添加响应状态码常量，提高代码可读性. Approval: 寸止(ID:1735373400). }}
/**
 * 系统常量定义
 */
const RESPONSE_CODES = {
  SUCCESS: 0,           // 操作成功
  GENERAL_ERROR: -1,    // 一般错误
  AUTH_ERROR: -2        // 认证错误（未登录）
}

const QUERY_CONSTANTS = {
  MAX_SHELVES_PER_QUERY: 100    // 单次查询最大货架数量
}

/**
 * 错误响应生成器
 *
 * 提供统一的错误响应格式和错误分类功能
 */
class ResponseHelper {
  /**
   * 创建成功响应
   * @param {*} data - 响应数据
   * @param {string} message - 成功消息
   * @returns {Object} 成功响应对象
   */
  static createSuccessResponse(data = null, message = '操作成功') {
    // {{ AURA-X: Add - 统一成功响应格式. Approval: 寸止(ID:**********). }}
    return {
      code: RESPONSE_CODES.SUCCESS,
      data,
      message
    }
  }

  /**
   * 创建错误响应
   * @param {number} code - 错误代码
   * @param {string} message - 错误消息
   * @param {Error} error - 原始错误对象（可选）
   * @returns {Object} 错误响应对象
   */
  static createErrorResponse(code, message, error = null) {
    // {{ AURA-X: Add - 统一错误响应格式，支持错误分类. Approval: 寸止(ID:**********). }}
    const response = {
      code,
      message
    }

    // 在开发环境中包含详细错误信息
    if (error && process.env.NODE_ENV === 'development') {
      response.error = {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    }

    return response
  }

  /**
   * 记录并创建错误响应
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   * @returns {Object} 错误响应对象
   */
  static logAndCreateErrorResponse(error, context) {
    // {{ AURA-X: Add - 错误日志记录和响应生成. Approval: 寸止(ID:**********). }}
    console.error(`[${context}] 操作失败:`, error)

    // 根据错误类型返回不同的错误码
    let code = RESPONSE_CODES.GENERAL_ERROR
    let message = error.message || '操作失败'

    // 特定错误类型的处理
    if (message.includes('未登录') || message.includes('token')) {
      code = RESPONSE_CODES.AUTH_ERROR
    }

    return this.createErrorResponse(code, message, error)
  }
}

/**
 * 验证用户身份令牌并获取用户ID
 *
 * 通过检查token的有效性和过期时间来验证用户身份
 *
 * @param {string} token - 用户身份令牌
 * @returns {string|null} 用户ID或null（验证失败）
 */
async function verifyToken(token) {
  // 检查token是否存在
  if (!token) {
    return null
  }

  try {
    // 在用户表中查找有效的token
    const userQueryResult = await db.collection('uni-id-users')
      .where({
        'token.token': token,                    // token值匹配
        'token.expire': dbCmd.gt(new Date()),   // token未过期
        status: 0                               // 用户状态正常
      })
      .get()

    // 如果找到有效用户，返回用户ID
    if (userQueryResult.data.length > 0) {
      return userQueryResult.data[0]._id
    }

    return null
  } catch (error) {
    console.error('验证用户token失败:', error)
    return null
  }
}

/**
 * 货架管理云函数主入口
 *
 * 提供客户端调用的各种货架管理接口，包括：
 * - 平台配置管理
 * - 货架数据同步
 * - 货架状态控制
 * - 操作日志查询
 *
 * @param {Object} event - 云函数事件对象
 * @param {string} event.action - 操作类型
 * @param {Object} event.data - 请求数据
 * @param {string} event._token - 用户身份令牌
 * @param {Object} context - 云函数上下文对象
 * @returns {Object} 操作结果
 */
exports.main = async (event, context) => {
  const { action, data, _token } = event

  // {{ AURA-X: Modify - 简化主函数逻辑，提高代码可读性. Approval: 寸止(ID:**********). }}

  // 步骤1：验证用户身份
  const authenticatedUserId = await verifyToken(_token)
  if (!authenticatedUserId) {
    return ResponseHelper.createErrorResponse(RESPONSE_CODES.AUTH_ERROR, '用户未登录或token已过期')
  }

  try {
    // 步骤2：执行对应的业务操作
    const operationResult = await executeAction(action, authenticatedUserId, data)
    return operationResult

  } catch (error) {
    // {{ AURA-X: Modify - 使用统一错误处理器处理云函数级错误. Approval: 寸止(ID:**********). }}
    return ResponseHelper.logAndCreateErrorResponse(error, `货架管理-${action}`)
  }
}
/**
 * 获取系统支持的平台列表
 *
 * 返回当前系统支持的所有平台信息，包括平台类型、名称、功能特性等
 *
 * @returns {Object} 包含平台列表的响应对象
 */
async function getPlatformList() {
  try {
    const supportedPlatforms = PlatformAdapterFactory.getSupportedPlatforms()

    return ResponseHelper.createSuccessResponse(supportedPlatforms, '获取平台列表成功')
  } catch (error) {
    return ResponseHelper.logAndCreateErrorResponse(error, '获取平台列表')
  }
}

/**
 * 保存或更新平台配置
 *
 * 为用户保存平台的登录配置信息，包括账号密码、token等
 *
 * @param {string} userId - 用户ID
 * @param {Object} configData - 平台配置数据
 * @returns {Object} 保存结果响应对象
 */
async function savePlatformConfig(userId, configData) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const databaseManager = new DatabaseManager()
    await databaseManager.savePlatformConfig(userId, configData)

    return {
      code: RESPONSE_CODES.SUCCESS,
      message: '保存成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 获取用户的平台配置列表
 */
async function getPlatformConfigs(userId) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    const configs = await dbManager.getPlatformConfigs(userId, null, true)

    // 隐藏敏感信息
    const safeConfigs = configs.map(config => {
      delete config.password
      delete config.token
      delete config.cookie
      delete config.headers
      return config
    })

    return {
      code: 0,
      data: safeConfigs,
      message: '获取成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 删除平台配置（级联删除）
 * {{ AURA-X: Modify - 使用DatabaseManager简化级联删除逻辑. Approval: 寸止(ID:1735374100). }}
 */
async function deletePlatformConfig(userId, configId) {
  const logger = new Logger()

  try {
    // 使用DatabaseManager执行级联删除
    const dbManager = new DatabaseManager()
    const deleteResult = await dbManager.cascadeDeletePlatformConfig(userId, configId)

    const { deletedShelves, platformName, platformType } = deleteResult

    // 记录删除日志
    await logger.log({
      user_id: userId,
      platform_type: platformType,
      action: 'delete_platform_config',
      status: 1,
      message: `删除平台配置"${platformName}"及其${deletedShelves}个关联货架`,
      trigger_type: 'manual'
    })

    return {
      code: 0,
      message: `删除成功，已删除平台配置及其${deletedShelves}个关联货架`,
      data: {
        deletedShelves: deletedShelves,
        platformName: platformName
      }
    }

  } catch (error) {
    console.error('删除平台配置失败:', error)

    // 记录错误日志
    await logger.log({
      user_id: userId,
      platform_type: 'unknown',
      action: 'delete_platform_config',
      status: 0,
      message: `删除平台配置失败: ${error.message}`,
      trigger_type: 'manual'
    })

    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 测试平台登录
 */
async function testPlatformLogin(userId, configData) {
  try {
    console.log('开始测试平台登录')
    console.log('用户ID:', userId)
    console.log('前端传递的配置数据:', configData)

    // {{ AURA-X: Modify - 使用配置ID精确获取配置，避免误操作多个同类型配置. Approval: 寸止(ID:1735373000). }}
    const dbManager = new DatabaseManager()
    let finalConfig = { ...configData }

    // 如果提供了配置ID，从数据库获取完整配置信息
    if (configData.configId) {
      // {{ AURA-X: Fix - 修复uniCloud数据库查询语法错误. Approval: 寸止(ID:1735373100). }}
      const result = await db.collection('platform-configs')
        .where({
          _id: configData.configId,
          user_id: userId // 确保安全性，只能操作自己的配置
        })
        .get()

      if (result.data.length > 0) {
        const existingConfig = result.data[0]
        console.log('找到数据库中的配置')

        // 使用数据库中的真实数据
        finalConfig.username = existingConfig.username
        finalConfig.password = existingConfig.password
        finalConfig.token = existingConfig.token
        finalConfig.cookie = existingConfig.cookie
        finalConfig.headers = existingConfig.headers || {}
        finalConfig._id = existingConfig._id // 保存配置ID用于后续更新
      }
    } else {
      // 兼容旧版本：如果没有配置ID，使用原有逻辑
      const dbConfigs = await dbManager.getPlatformConfigs(userId, configData.platformType, false)
      if (dbConfigs.length > 0) {
        const existingConfig = dbConfigs[0]
        console.log('找到数据库中的配置（兼容模式）')

        finalConfig.username = existingConfig.username
        finalConfig.password = existingConfig.password
        finalConfig.token = existingConfig.token
        finalConfig.cookie = existingConfig.cookie
        finalConfig.headers = existingConfig.headers || {}
        finalConfig._id = existingConfig._id
      }
    }

    console.log('最终使用的配置数据:', {
      configId: finalConfig._id,
      platformType: finalConfig.platformType,
      username: finalConfig.username,
      hasPassword: !!finalConfig.password,
      hasToken: !!finalConfig.token,
      hasCookie: !!finalConfig.cookie
    })

    const adapter = PlatformAdapterFactory.create(finalConfig.platformType, {
      ...finalConfig,
      user_id: userId
    })

    console.log('适配器创建成功，开始登录')
    const loginResult = await adapter.login()
    console.log('登录结果:', loginResult)

    // {{ AURA-X: Delete - 移除云函数层的重复数据库更新，适配器内部已处理. Approval: 寸止(ID:1735373000). }}
    // 适配器内部已经处理了数据库更新，这里不再重复更新

    return {
      code: loginResult.success ? 0 : -1,
      message: loginResult.message
    }
  } catch (error) {
    console.error('测试平台登录异常:', error)
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 更新货架数据
 */
async function syncShelves(userId, platformType) {
  try {
    const logger = new Logger()
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    // 获取平台配置
    const dbManager = new DatabaseManager()
    const platformConfigs = await dbManager.getPlatformConfigs(userId, platformType, true)

    if (platformConfigs.length === 0) {
      throw new Error('未找到平台配置')
    }
    const config = platformConfigs[0]
    const adapter = PlatformAdapterFactory.create(platformType, config)
    // {{ AURA-X: Modify - 优化登录状态检查，登录状态检测已移至统一的API响应处理中. Approval: 寸止(ID:**********). }}
    // 检查基本登录状态（token是否存在）
    const isLoggedIn = await adapter.checkLoginStatus()
    if (!isLoggedIn) {
      // 尝试自动登录
      if (config.auto_login) {
        const loginResult = await adapter.login()
        if (!loginResult.success) {
          throw new Error('登录失败: ' + loginResult.message)
        }
      } else {
        throw new Error('平台未登录，请手动更新Cookie')
      }
    }
    // 获取货架列表
    const shelfList = await adapter.getShelfList()
    // {{ AURA-X: Modify - 重构批量货架操作，支持去重和删除检测. Approval: 寸止(ID:1735373200). }}
    // 批量更新或插入货架数据（支持去重和删除检测）
    const syncResult = await dbManager.batchUpsertShelves(userId, platformType, shelfList)

    // 构建详细的同步消息
    const { stats } = syncResult
    const details = []
    if (stats.created > 0) details.push(`新增${stats.created}个`)
    if (stats.updated > 0) details.push(`更新${stats.updated}个`)
    if (stats.deleted > 0) details.push(`删除${stats.deleted}个`)
    if (stats.duplicatesRemoved > 0) details.push(`去重${stats.duplicatesRemoved}个`)

    const message = `同步完成，${details.join('，')}货架`

    // 记录更新日志
    await logger.log({
      user_id: userId,
      platform_type: platformType,
      action: 'sync',
      status: 1,
      message: message,
      trigger_type: 'manual'
    })

    return {
      code: 0,
      data: {
        syncCount: syncResult.total,
        stats: stats,
        details: message
      },
      message: message
    }
  } catch (error) {
    const logger = new Logger()
    await logger.log({
      user_id: userId,
      platform_type: platformType,
      action: 'sync',
      status: 0,
      message: error.message,
      trigger_type: 'manual'
    })
    return {
      code: -1,
      message: error.message
    }
  }
}

/**
 * 清理重复的游戏账号记录
 */
async function cleanupDuplicateAccounts(userId, platformType) {
  try {
    const logger = new Logger()
    const dbManager = new DatabaseManager()

    // 执行清理操作
    const cleanupResult = await dbManager.cleanupDuplicateAccounts(userId, platformType)

    // 记录清理日志
    await logger.log({
      user_id: userId,
      platform_type: platformType || 'all',
      action: 'cleanup_duplicates',
      status: 1,
      message: `清理完成，删除了 ${cleanupResult.duplicatesRemoved} 条重复记录`,
      trigger_type: 'manual'
    })

    return {
      code: 0,
      data: cleanupResult,
      message: `清理完成，删除了 ${cleanupResult.duplicatesRemoved} 条重复记录`
    }
  } catch (error) {
    const logger = new Logger()
    await logger.log({
      user_id: userId,
      platform_type: platformType || 'all',
      action: 'cleanup_duplicates',
      status: 0,
      message: error.message,
      trigger_type: 'manual'
    })
    return {
      code: -1,
      message: error.message
    }
  }
}

/**
 * 获取货架列表
 */
async function getShelfList(userId, queryData = {}) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    const result = await dbManager.getShelfList(userId, queryData)

    return {
      code: 0,
      data: result,
      message: '获取成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 手动切换货架状态
 */
async function toggleShelfStatus(userId, shelfData) {
  try {
    const { shelfId, targetStatus } = shelfData
    const logger = new Logger()
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()

    // 获取货架信息
    const shelfResult = await dbManager.getShelfList(userId, { pageSize: QUERY_CONSTANTS.MAX_SHELVES_PER_QUERY })
    const shelfInfo = shelfResult.list.find(s => s._id === shelfId)

    if (!shelfInfo) {
      throw new Error('货架不存在')
    }

    // 获取平台配置
    const platformConfigs = await dbManager.getPlatformConfigs(userId, shelfInfo.platform_type, true)
    if (platformConfigs.length === 0) {
      throw new Error('平台配置不存在')
    }
    const config = platformConfigs[0]
    const adapter = PlatformAdapterFactory.create(shelfInfo.platform_type, config)
    // 验证目标状态
    if (!StateManager.isValidState(targetStatus)) {
      throw new Error('无效的目标状态')
    }

    // 执行上下架操作
    let result
    let action
    if (targetStatus === StateManager.STATES.AVAILABLE) {
      // 上架
      result = await adapter.onShelf(shelfInfo.platform_shelf_id)
      action = 'on_shelf'
    } else if (targetStatus === StateManager.STATES.OFFLINE) {
      // 下架
      result = await adapter.offShelf(shelfInfo.platform_shelf_id)
      action = 'off_shelf'
    } else {
      throw new Error('不支持的目标状态')
    }
    // 记录操作日志
    await logger.log({
      user_id: userId,
      platform_type: shelfInfo.platform_type,
      platform_shelf_id: shelfInfo.platform_shelf_id,
      game_account: shelfInfo.game_account,
      action: action,
      status: result.success ? 1 : 0,
      message: result.message,
      trigger_type: 'manual'
    })
    if (result.success) {
      // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
      // 更新本地状态
      await dbManager.updateShelfStatus(shelfId, {
        unified_state: targetStatus,
        platform_status: {}
      })
    }
    return {
      code: result.success ? 0 : -1,
      message: result.message
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 切换货架监控状态
 */
async function toggleMonitorStatus(userId, shelfData) {
  try {
    const { shelfId, isActive } = shelfData
    const logger = new Logger()
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()

    // 获取货架信息
    const shelfResult = await dbManager.getShelfList(userId, { pageSize: QUERY_CONSTANTS.MAX_SHELVES_PER_QUERY })
    const shelfInfo = shelfResult.list.find(s => s._id === shelfId)

    if (!shelfInfo) {
      throw new Error('货架不存在')
    }

    // 更新监控状态
    await dbManager.updateShelfMonitorStatus(shelfId, isActive)
    
    // 记录操作日志
    await logger.log({
      user_id: userId,
      platform_type: shelfInfo.platform_type,
      platform_shelf_id: shelfInfo.platform_shelf_id,
      game_account: shelfInfo.game_account,
      action: isActive ? 'enable_monitor' : 'disable_monitor',
      status: 1,
      message: isActive ? '开启监控' : '关闭监控',
      trigger_type: 'manual'
    })
    
    return {
      code: 0,
      message: isActive ? '已开启监控' : '已关闭监控'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
// {{ AURA-X: Remove - 已被ResponseHelper.createErrorResponse替代. Approval: 寸止(ID:**********). }}
// 原createErrorResponse函数已被ResponseHelper.createErrorResponse替代

/**
 * 执行动作处理
 * @param {string} action 动作类型
 * @param {string} uid 用户ID
 * @param {Object} data 请求数据
 * @returns {Object} 处理结果
 */
async function executeAction(action, uid, data) {
  // {{ AURA-X: Add - 使用动作处理器映射替代长switch语句，提高可维护性. Approval: 寸止(ID:**********). }}
  const actionHandlers = {
    'getPlatformList': () => getPlatformList(),
    'savePlatformConfig': () => savePlatformConfig(uid, data),
    'getPlatformConfigs': () => getPlatformConfigs(uid),
    'deletePlatformConfig': () => deletePlatformConfig(uid, data.configId),
    'testPlatformLogin': () => testPlatformLogin(uid, data),
    'syncShelves': () => syncShelves(uid, data.platformType),
    'getShelfList': () => getShelfList(uid, data),
    'getShelfStats': () => getShelfStats(uid), // {{ AURA-X: Add - 新增货架统计接口. Approval: 寸止(ID:**********). }}
    'toggleShelfStatus': () => toggleShelfStatus(uid, data),
    'toggleMonitorStatus': () => toggleMonitorStatus(uid, data),
    'getOperationLogs': () => getOperationLogs(uid, data),
    'cleanupDuplicates': () => cleanupDuplicateAccounts(uid, data.platformType),
    // {{ AURA-X: Add - 新增账号管理相关接口. Approval: 寸止(ID:**********). }}
    'getAccountList': () => getAccountList(uid, data),
    'batchOffShelfByAccount': () => batchOffShelfByAccount(uid, data),
    'batchOnShelfByAccount': () => batchOnShelfByAccount(uid, data), // {{ AURA-X: Add - 新增批量上架接口. Approval: 寸止(ID:**********). }}
    'toggleAccountMonitor': () => toggleAccountMonitor(uid, data),

    // {{ AURA-X: Add - 新增定时任务管理接口. Approval: 寸止(ID:**********). }}
    'createScheduledTask': () => createScheduledTask(uid, data),
    'cancelScheduledTask': () => cancelScheduledTask(uid, data),
    'getScheduledTasks': () => getScheduledTasks(uid, data)
  }

  const handler = actionHandlers[action]
  if (!handler) {
    return ResponseHelper.createErrorResponse(RESPONSE_CODES.GENERAL_ERROR, '不支持的操作')
  }

  return await handler()
}

/**
 * 获取货架统计信息
 *
 * 提供轻量级的统计数据接口，避免传输完整的货架列表
 *
 * @param {string} userId - 用户ID
 * @returns {Object} 统计信息响应
 */
async function getShelfStats(userId) {
  try {
    // {{ AURA-X: Add - 新增货架统计接口，优化前端数据加载性能. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()

    // 获取货架统计数据（只查询必要字段，提高性能）
    const shelfResult = await dbManager.getShelfList(userId, {
      pageSize: QUERY_CONSTANTS.MAX_SHELVES_PER_QUERY,
      fields: ['unified_state', 'is_active'] // 只查询统计需要的字段
    })

    const shelves = shelfResult.list

    // 计算基础统计
    const totalShelves = shelves.length
    const activeShelves = shelves.filter(shelf => shelf.is_active).length

    // 使用StateManager计算状态统计
    const StateManager = require('shelf-core').StateManager
    const stateStats = StateManager.getStateStats(shelves)

    // 计算状态分布百分比
    const statePercentages = StateManager.getStatePercentages(shelves)

    return ResponseHelper.createSuccessResponse({
      total: totalShelves,
      active: activeShelves,
      inactive: totalShelves - activeShelves,
      states: {
        available: stateStats.available,
        rented: stateStats.rented,
        offline: stateStats.offline,
        other: stateStats.other
      },
      percentages: statePercentages
    }, '获取统计信息成功')

  } catch (error) {
    return ResponseHelper.logAndCreateErrorResponse(error, '获取货架统计')
  }
}

/**
 * 获取操作日志
 */
async function getOperationLogs(userId, queryData = {}) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    const result = await dbManager.getOperationLogs(userId, queryData)

    return {
      code: 0,
      data: result,
      message: '获取成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}

/**
 * 获取账号列表（按游戏账号聚合）
 * {{ AURA-X: Add - 新增账号管理核心接口，按game_account聚合货架数据. Approval: 寸止(ID:**********). }}
 *
 * @param {string} userId - 用户ID
 * @param {Object} queryData - 查询参数
 * @param {string} queryData.searchKeyword - 搜索关键词
 * @param {string} queryData.platformType - 平台类型筛选
 * @param {number} queryData.pageIndex - 页码
 * @param {number} queryData.pageSize - 每页数量
 * @returns {Object} 账号列表响应
 */
async function getAccountList(userId, queryData = {}) {
  try {
    const dbManager = new DatabaseManager()

    // 构建查询条件
    const where = { user_id: userId }

    // 平台类型筛选
    if (queryData.platformType && queryData.platformType !== 'all') {
      where.platform_type = queryData.platformType
    }

    // 搜索关键词筛选
    if (queryData.searchKeyword && queryData.searchKeyword.trim()) {
      const keyword = queryData.searchKeyword.trim()
      where.game_account = dbCmd.regex({
        regexp: keyword,
        options: 'i'
      })
    }

    // 获取所有符合条件的货架数据
    const shelfResult = await db.collection('account-shelves')
      .where(where)
      .orderBy('last_sync_time', 'desc')
      .get()

    const shelves = shelfResult.data

    // 按game_account分组聚合数据
    const accountMap = new Map()

    shelves.forEach(shelf => {
      const accountKey = shelf.game_account

      if (!accountMap.has(accountKey)) {
        accountMap.set(accountKey, {
          game_account: accountKey,
          platforms: [],
          total_shelves: 0,
          active_shelves: 0,
          states: {
            available: 0,
            rented: 0,
            offline: 0,
            other: 0
          },
          last_sync_time: shelf.last_sync_time,
          is_monitoring: false // 默认值，后续计算
        })
      }

      const account = accountMap.get(accountKey)

      // 更新平台信息
      let platformInfo = account.platforms.find(p => p.platform_type === shelf.platform_type)
      if (!platformInfo) {
        platformInfo = {
          platform_type: shelf.platform_type,
          platform_name: getPlatformNameByType(shelf.platform_type),
          shelf_count: 0,
          active_count: 0,
          states: { available: 0, rented: 0, offline: 0, other: 0 },
          // {{ AURA-X: Add - 添加货架详情列表，显示具体的货架标题. Approval: 寸止(ID:**********). }}
          shelves: []
        }
        account.platforms.push(platformInfo)
      }

      // 统计数据
      account.total_shelves++
      platformInfo.shelf_count++

      if (shelf.is_active) {
        account.active_shelves++
        platformInfo.active_count++
      }

      // 状态统计
      const stateKey = getStateKey(shelf.unified_state)
      account.states[stateKey]++
      platformInfo.states[stateKey]++

      // {{ AURA-X: Add - 添加货架详情到平台信息中. Approval: 寸止(ID:**********). }}
      // 添加货架详情
      platformInfo.shelves.push({
        _id: shelf._id,
        shelf_title: shelf.shelf_title,
        game_name: shelf.game_name,
        rent_price: shelf.rent_price,
        unified_state: shelf.unified_state,
        is_active: shelf.is_active,
        last_sync_time: shelf.last_sync_time
      })

      // 更新最后同步时间（取最新的）
      if (shelf.last_sync_time > account.last_sync_time) {
        account.last_sync_time = shelf.last_sync_time
      }
    })

    // 转换为数组并计算监控状态
    const accountList = Array.from(accountMap.values()).map(account => {
      // {{ AURA-X: Modify - 优化账号监控状态逻辑，只有所有货架都启用监控才认为账号启用监控. Approval: 寸止(ID:**********). }}
      // 计算监控状态：只有当所有货架都启用监控时，才认为账号完全启用监控
      account.is_monitoring = account.total_shelves > 0 && account.active_shelves === account.total_shelves

      // 添加部分监控状态标识
      account.partial_monitoring = account.active_shelves > 0 && account.active_shelves < account.total_shelves

      return account
    })

    // 分页处理
    const pageIndex = queryData.pageIndex || 1
    const pageSize = queryData.pageSize || 20
    const startIndex = (pageIndex - 1) * pageSize
    const endIndex = startIndex + pageSize

    const paginatedList = accountList.slice(startIndex, endIndex)

    return ResponseHelper.createSuccessResponse({
      list: paginatedList,
      total: accountList.length,
      pageIndex: pageIndex,
      pageSize: pageSize,
      hasMore: endIndex < accountList.length
    }, '获取账号列表成功')

  } catch (error) {
    return ResponseHelper.logAndCreateErrorResponse(error, '获取账号列表')
  }
}

/**
 * 获取状态对应的键名
 * @param {number} state - 统一状态码
 * @returns {string} 状态键名
 */
function getStateKey(state) {
  switch (state) {
    case 0: return 'available'
    case 1: return 'rented'
    case -1: return 'offline'
    default: return 'other'
  }
}

/**
 * 根据平台类型获取平台名称
 * @param {string} platformType - 平台类型
 * @returns {string} 平台名称
 */
function getPlatformNameByType(platformType) {
  const platformMap = {
    'zuhaowan': '租号玩',
    'uhaozu': 'U号租'
  }
  return platformMap[platformType] || platformType
}

/**
 * 按账号批量下架
 * {{ AURA-X: Add - 新增账号批量下架功能，支持一键下架指定账号的所有货架. Approval: 寸止(ID:**********). }}
 *
 * @param {string} userId - 用户ID
 * @param {Object} data - 操作数据
 * @param {string} data.gameAccount - 游戏账号名
 * @param {Array} data.platformTypes - 要下架的平台类型列表（可选，默认全部平台）
 * @returns {Object} 批量下架结果
 */
async function batchOffShelfByAccount(userId, data) {
  const { gameAccount, platformTypes } = data
  const logger = new Logger()

  if (!gameAccount) {
    return ResponseHelper.createErrorResponse(RESPONSE_CODES.GENERAL_ERROR, '游戏账号不能为空')
  }

  try {
    const dbManager = new DatabaseManager()

    // 构建查询条件
    const where = {
      user_id: userId,
      game_account: gameAccount,
      unified_state: StateManager.STATES.AVAILABLE // 只下架待租状态的货架
    }

    // 如果指定了平台类型，添加平台筛选
    if (platformTypes && platformTypes.length > 0) {
      where.platform_type = dbCmd.in(platformTypes)
    }

    // 获取要下架的货架列表
    const shelfResult = await db.collection('account-shelves')
      .where(where)
      .get()

    const shelvesToOffShelf = shelfResult.data

    if (shelvesToOffShelf.length === 0) {
      return ResponseHelper.createSuccessResponse({
        processedCount: 0,
        successCount: 0,
        failedCount: 0,
        details: []
      }, '没有找到可下架的货架')
    }

    // {{ AURA-X: Add - 检查是否有出租中的货架，阻止上下架操作. Approval: 寸止(ID:**********). }}
    // 检查该账号是否有出租中的货架
    const rentedCheckResult = await db.collection('account-shelves')
      .where({
        user_id: userId,
        game_account: gameAccount,
        unified_state: StateManager.STATES.RENTED // 出租中状态
      })
      .get()

    if (rentedCheckResult.data.length > 0) {
      return ResponseHelper.createErrorResponse(
        RESPONSE_CODES.GENERAL_ERROR,
        `账号"${gameAccount}"有${rentedCheckResult.data.length}个货架正在出租中，无法执行批量下架操作`
      )
    }

    // 按平台分组处理
    const platformGroups = {}
    shelvesToOffShelf.forEach(shelf => {
      if (!platformGroups[shelf.platform_type]) {
        platformGroups[shelf.platform_type] = []
      }
      platformGroups[shelf.platform_type].push(shelf)
    })

    const results = {
      processedCount: shelvesToOffShelf.length,
      successCount: 0,
      failedCount: 0,
      details: []
    }

    // 逐平台处理下架操作
    for (const [platformType, shelves] of Object.entries(platformGroups)) {
      try {
        // 获取平台配置
        const platformConfigs = await dbManager.getPlatformConfigs(userId, platformType, true)
        if (platformConfigs.length === 0) {
          // 平台配置不存在，跳过该平台
          results.failedCount += shelves.length
          results.details.push({
            platform_type: platformType,
            platform_name: getPlatformNameByType(platformType),
            success: false,
            message: '平台配置不存在',
            shelf_count: shelves.length
          })
          continue
        }

        const config = platformConfigs[0]
        const adapter = PlatformAdapterFactory.create(platformType, config)

        let platformSuccessCount = 0
        let platformFailedCount = 0

        // 逐个下架该平台的货架
        for (const shelf of shelves) {
          try {
            const result = await adapter.offShelf(shelf.platform_shelf_id)

            if (result.success) {
              // 更新本地状态
              await dbManager.updateShelfStatus(shelf._id, {
                unified_state: StateManager.STATES.OFFLINE,
                platform_status: {}
              })
              platformSuccessCount++

              // 记录成功日志
              await logger.log({
                user_id: userId,
                platform_type: platformType,
                platform_shelf_id: shelf.platform_shelf_id,
                game_account: gameAccount,
                action: 'batch_off_shelf',
                status: 1,
                message: `批量下架成功: ${shelf.shelf_title}`,
                trigger_type: 'manual'
              })
            } else {
              platformFailedCount++

              // 记录失败日志
              await logger.log({
                user_id: userId,
                platform_type: platformType,
                platform_shelf_id: shelf.platform_shelf_id,
                game_account: gameAccount,
                action: 'batch_off_shelf',
                status: 0,
                message: `批量下架失败: ${result.message}`,
                trigger_type: 'manual'
              })
            }
          } catch (shelfError) {
            platformFailedCount++
            console.error(`下架货架失败 [${shelf.platform_shelf_id}]:`, shelfError)
          }
        }

        results.successCount += platformSuccessCount
        results.failedCount += platformFailedCount
        results.details.push({
          platform_type: platformType,
          platform_name: getPlatformNameByType(platformType),
          success: platformFailedCount === 0,
          message: `成功${platformSuccessCount}个，失败${platformFailedCount}个`,
          shelf_count: shelves.length,
          success_count: platformSuccessCount,
          failed_count: platformFailedCount
        })

      } catch (platformError) {
        console.error(`处理平台${platformType}时出错:`, platformError)
        results.failedCount += shelves.length
        results.details.push({
          platform_type: platformType,
          platform_name: getPlatformNameByType(platformType),
          success: false,
          message: platformError.message,
          shelf_count: shelves.length
        })
      }
    }

    // 生成总结消息
    const message = `批量下架完成：处理${results.processedCount}个货架，成功${results.successCount}个，失败${results.failedCount}个`

    return ResponseHelper.createSuccessResponse(results, message)

  } catch (error) {
    return ResponseHelper.logAndCreateErrorResponse(error, '批量下架')
  }
}

/**
 * 切换账号监控状态
 * {{ AURA-X: Add - 新增账号级监控管理功能，支持按账号统一控制监控状态. Approval: 寸止(ID:**********). }}
 *
 * @param {string} userId - 用户ID
 * @param {Object} data - 操作数据
 * @param {string} data.gameAccount - 游戏账号名
 * @param {boolean} data.isActive - 监控状态
 * @param {Array} data.platformTypes - 要操作的平台类型列表（可选，默认全部平台）
 * @returns {Object} 监控状态切换结果
 */
async function toggleAccountMonitor(userId, data) {
  const { gameAccount, isActive, platformTypes } = data
  const logger = new Logger()

  if (!gameAccount) {
    return ResponseHelper.createErrorResponse(RESPONSE_CODES.GENERAL_ERROR, '游戏账号不能为空')
  }

  if (typeof isActive !== 'boolean') {
    return ResponseHelper.createErrorResponse(RESPONSE_CODES.GENERAL_ERROR, '监控状态参数无效')
  }

  try {
    const dbManager = new DatabaseManager()

    // 构建查询条件
    const where = {
      user_id: userId,
      game_account: gameAccount
    }

    // 如果指定了平台类型，添加平台筛选
    if (platformTypes && platformTypes.length > 0) {
      where.platform_type = dbCmd.in(platformTypes)
    }

    // 获取要更新的货架列表
    const shelfResult = await db.collection('account-shelves')
      .where(where)
      .get()

    const shelvesToUpdate = shelfResult.data

    if (shelvesToUpdate.length === 0) {
      return ResponseHelper.createSuccessResponse({
        updatedCount: 0,
        details: []
      }, '没有找到相关货架')
    }

    // 批量更新监控状态
    const updatePromises = shelvesToUpdate.map(shelf =>
      dbManager.updateShelfMonitorStatus(shelf._id, isActive)
    )

    await Promise.all(updatePromises)

    // 按平台统计更新结果
    const platformStats = {}
    shelvesToUpdate.forEach(shelf => {
      if (!platformStats[shelf.platform_type]) {
        platformStats[shelf.platform_type] = {
          platform_name: getPlatformNameByType(shelf.platform_type),
          count: 0
        }
      }
      platformStats[shelf.platform_type].count++
    })

    const details = Object.entries(platformStats).map(([platformType, stats]) => ({
      platform_type: platformType,
      platform_name: stats.platform_name,
      updated_count: stats.count
    }))

    // 记录操作日志
    await logger.log({
      user_id: userId,
      platform_type: 'all',
      game_account: gameAccount,
      action: isActive ? 'enable_account_monitor' : 'disable_account_monitor',
      status: 1,
      message: `${isActive ? '开启' : '关闭'}账号监控，影响${shelvesToUpdate.length}个货架`,
      trigger_type: 'manual'
    })

    const message = `${isActive ? '开启' : '关闭'}账号监控成功，已更新${shelvesToUpdate.length}个货架`

    return ResponseHelper.createSuccessResponse({
      updatedCount: shelvesToUpdate.length,
      details: details
    }, message)

  } catch (error) {
    return ResponseHelper.logAndCreateErrorResponse(error, '切换账号监控状态')
  }
}

/**
 * 按账号批量上架
 * {{ AURA-X: Add - 新增账号批量上架功能，支持一键上架指定账号的所有下架货架. Approval: 寸止(ID:**********). }}
 *
 * @param {string} userId - 用户ID
 * @param {Object} data - 操作数据
 * @param {string} data.gameAccount - 游戏账号名
 * @param {Array} data.platformTypes - 要上架的平台类型列表（可选，默认全部平台）
 * @returns {Object} 批量上架结果
 */
async function batchOnShelfByAccount(userId, data) {
  const { gameAccount, platformTypes } = data
  const logger = new Logger()

  if (!gameAccount) {
    return ResponseHelper.createErrorResponse(RESPONSE_CODES.GENERAL_ERROR, '游戏账号不能为空')
  }

  try {
    const dbManager = new DatabaseManager()

    // 构建查询条件
    const where = {
      user_id: userId,
      game_account: gameAccount,
      unified_state: StateManager.STATES.OFFLINE // 只上架已下架状态的货架
    }

    // 如果指定了平台类型，添加平台筛选
    if (platformTypes && platformTypes.length > 0) {
      where.platform_type = dbCmd.in(platformTypes)
    }

    // 获取要上架的货架列表
    const shelfResult = await db.collection('account-shelves')
      .where(where)
      .get()

    const shelvesToOnShelf = shelfResult.data

    if (shelvesToOnShelf.length === 0) {
      return ResponseHelper.createSuccessResponse({
        processedCount: 0,
        successCount: 0,
        failedCount: 0,
        details: []
      }, '没有找到可上架的货架')
    }

    // {{ AURA-X: Add - 检查是否有出租中的货架，阻止上下架操作. Approval: 寸止(ID:**********). }}
    // 检查该账号是否有出租中的货架
    const rentedCheckResult = await db.collection('account-shelves')
      .where({
        user_id: userId,
        game_account: gameAccount,
        unified_state: StateManager.STATES.RENTED // 出租中状态
      })
      .get()

    if (rentedCheckResult.data.length > 0) {
      return ResponseHelper.createErrorResponse(
        RESPONSE_CODES.GENERAL_ERROR,
        `账号"${gameAccount}"有${rentedCheckResult.data.length}个货架正在出租中，无法执行批量上架操作`
      )
    }

    // 按平台分组处理
    const platformGroups = {}
    shelvesToOnShelf.forEach(shelf => {
      if (!platformGroups[shelf.platform_type]) {
        platformGroups[shelf.platform_type] = []
      }
      platformGroups[shelf.platform_type].push(shelf)
    })

    const results = {
      processedCount: shelvesToOnShelf.length,
      successCount: 0,
      failedCount: 0,
      details: []
    }

    // 逐平台处理上架操作
    for (const [platformType, shelves] of Object.entries(platformGroups)) {
      try {
        // 获取平台配置
        const platformConfigs = await dbManager.getPlatformConfigs(userId, platformType, true)
        if (platformConfigs.length === 0) {
          // 平台配置不存在，跳过该平台
          results.failedCount += shelves.length
          results.details.push({
            platform_type: platformType,
            platform_name: getPlatformNameByType(platformType),
            success: false,
            message: '平台配置不存在',
            shelf_count: shelves.length
          })
          continue
        }

        const config = platformConfigs[0]
        const adapter = PlatformAdapterFactory.create(platformType, config)

        let platformSuccessCount = 0
        let platformFailedCount = 0

        // 逐个上架该平台的货架
        for (const shelf of shelves) {
          try {
            const result = await adapter.onShelf(shelf.platform_shelf_id)

            if (result.success) {
              // 更新本地状态
              await dbManager.updateShelfStatus(shelf._id, {
                unified_state: StateManager.STATES.AVAILABLE,
                platform_status: {}
              })
              platformSuccessCount++

              // 记录成功日志
              await logger.log({
                user_id: userId,
                platform_type: platformType,
                platform_shelf_id: shelf.platform_shelf_id,
                game_account: gameAccount,
                action: 'batch_on_shelf',
                status: 1,
                message: `批量上架成功: ${shelf.shelf_title}`,
                trigger_type: 'manual'
              })
            } else {
              platformFailedCount++

              // 记录失败日志
              await logger.log({
                user_id: userId,
                platform_type: platformType,
                platform_shelf_id: shelf.platform_shelf_id,
                game_account: gameAccount,
                action: 'batch_on_shelf',
                status: 0,
                message: `批量上架失败: ${result.message}`,
                trigger_type: 'manual'
              })
            }
          } catch (shelfError) {
            platformFailedCount++
            console.error(`上架货架失败 [${shelf.platform_shelf_id}]:`, shelfError)
          }
        }

        results.successCount += platformSuccessCount
        results.failedCount += platformFailedCount
        results.details.push({
          platform_type: platformType,
          platform_name: getPlatformNameByType(platformType),
          success: platformFailedCount === 0,
          message: `成功${platformSuccessCount}个，失败${platformFailedCount}个`,
          shelf_count: shelves.length,
          success_count: platformSuccessCount,
          failed_count: platformFailedCount
        })

      } catch (platformError) {
        console.error(`处理平台${platformType}时出错:`, platformError)
        results.failedCount += shelves.length
        results.details.push({
          platform_type: platformType,
          platform_name: getPlatformNameByType(platformType),
          success: false,
          message: platformError.message,
          shelf_count: shelves.length
        })
      }
    }

    // 生成总结消息
    const message = `批量上架完成：处理${results.processedCount}个货架，成功${results.successCount}个，失败${results.failedCount}个`

    return ResponseHelper.createSuccessResponse(results, message)

  } catch (error) {
    return ResponseHelper.logAndCreateErrorResponse(error, '批量上架')
  }
}

/**
 * 创建定时任务
 *
 * @param {string} userId - 用户ID
 * @param {Object} taskData - 任务数据
 * @returns {Object} 创建结果
 */
async function createScheduledTask(userId, taskData) {
  try {
    const taskManager = new ScheduledTaskManager()

    // 添加用户ID到任务数据
    const fullTaskData = {
      ...taskData,
      userId: userId
    }

    const result = await taskManager.createTask(fullTaskData)

    if (result.success) {
      return ResponseHelper.createSuccessResponse({
        taskId: result.taskId,
        executeTime: result.executeTime
      }, result.message)
    } else {
      return ResponseHelper.createErrorResponse(RESPONSE_CODES.GENERAL_ERROR, result.message)
    }

  } catch (error) {
    return ResponseHelper.logAndCreateErrorResponse(error, '创建定时任务')
  }
}

/**
 * 取消定时任务
 *
 * @param {string} userId - 用户ID
 * @param {Object} data - 请求数据
 * @returns {Object} 取消结果
 */
async function cancelScheduledTask(userId, data) {
  try {
    const { taskId } = data

    if (!taskId) {
      return ResponseHelper.createErrorResponse(RESPONSE_CODES.GENERAL_ERROR, '任务ID不能为空')
    }

    const taskManager = new ScheduledTaskManager()
    const result = await taskManager.cancelTask(taskId, userId)

    if (result.success) {
      return ResponseHelper.createSuccessResponse(null, result.message)
    } else {
      return ResponseHelper.createErrorResponse(RESPONSE_CODES.GENERAL_ERROR, result.message)
    }

  } catch (error) {
    return ResponseHelper.logAndCreateErrorResponse(error, '取消定时任务')
  }
}

/**
 * 获取用户的定时任务列表
 *
 * @param {string} userId - 用户ID
 * @param {Object} data - 查询参数
 * @returns {Object} 任务列表
 */
async function getScheduledTasks(userId, data) {
  try {
    const taskManager = new ScheduledTaskManager()
    const result = await taskManager.getUserTasks(userId, data)

    return ResponseHelper.createSuccessResponse(result, '获取定时任务列表成功')

  } catch (error) {
    return ResponseHelper.logAndCreateErrorResponse(error, '获取定时任务列表')
  }
}
