<template>
  <view class="responsive-grid" :class="gridClass" :style="gridStyle">
    <slot></slot>
  </view>
</template>

<script>
/**
 * 响应式网格组件
 * 提供灵活的网格布局，支持不同屏幕尺寸的自适应列数
 */
export default {
  name: 'ResponsiveGrid',
  props: {
    // 移动端列数
    mobileCols: {
      type: Number,
      default: 1
    },
    // 平板端列数
    tabletCols: {
      type: Number,
      default: 2
    },
    // 桌面端列数
    desktopCols: {
      type: Number,
      default: 3
    },
    // 大屏桌面端列数
    largeDesktopCols: {
      type: Number,
      default: 4
    },
    // 网格间距大小
    gap: {
      type: String,
      default: 'base',
      validator: value => ['xs', 'sm', 'base', 'lg', 'xl'].includes(value)
    },
    // 是否使用等高布局
    equalHeight: {
      type: Boolean,
      default: false
    },
    // 自定义网格模板
    customTemplate: {
      type: String,
      default: ''
    }
  },
  computed: {
    gridClass() {
      return {
        'equal-height': this.equalHeight,
        [`gap-${this.gap}`]: true
      }
    },
    gridStyle() {
      const style = {}
      
      if (this.customTemplate) {
        style.gridTemplateColumns = this.customTemplate
      }
      
      return style
    }
  }
}
</script>

<style lang="scss" scoped>
.responsive-grid {
  display: grid;
  width: 100%;
  
  // 默认移动端布局
  grid-template-columns: repeat(v-bind(mobileCols), 1fr);
  
  // 平板端布局
  @include tablet-up {
    grid-template-columns: repeat(v-bind(tabletCols), 1fr);
  }
  
  // 桌面端布局
  @include desktop-up {
    grid-template-columns: repeat(v-bind(desktopCols), 1fr);
  }
  
  // 大屏桌面端布局
  @include large-desktop-up {
    grid-template-columns: repeat(v-bind(largeDesktopCols), 1fr);
  }
  
  // 等高布局
  &.equal-height {
    align-items: stretch;
    
    :deep(> *) {
      height: 100%;
    }
  }
  
  // 间距设置
  &.gap-xs {
    gap: $spacing-xs;
    
    @include desktop-up {
      gap: $spacing-xs-pc;
    }
  }
  
  &.gap-sm {
    gap: $spacing-sm;
    
    @include desktop-up {
      gap: $spacing-sm-pc;
    }
  }
  
  &.gap-base {
    gap: $spacing-base;
    
    @include desktop-up {
      gap: $spacing-base-pc;
    }
  }
  
  &.gap-lg {
    gap: $spacing-lg;
    
    @include desktop-up {
      gap: $spacing-lg-pc;
    }
  }
  
  &.gap-xl {
    gap: $spacing-xl;
    
    @include desktop-up {
      gap: $spacing-xl-pc;
    }
  }
}

/* 网格项通用样式 */
:deep(.grid-item) {
  width: 100%;
  
  // 移动端优化
  @include mobile-only {
    margin-bottom: $spacing-sm;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
