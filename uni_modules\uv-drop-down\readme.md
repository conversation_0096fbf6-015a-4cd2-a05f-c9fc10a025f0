## DropDown 下拉筛选

> **组件名：uv-drop-down**

该组件主要提供筛选下拉筛选框，内置基础筛选功能，可以根据自己的需求自定义筛选项。

为了兼容app-nvue，需要内置三个组件进行配合使用，uv-drop-down属于菜单项（其实还包括子组件uv-drop-down-item），uv-drop-down-popup属于筛选框。

只需要做简单的约定式配置，即可使用该功能，兼容性良好，已经在多端进行了多次测试。

# <a href="https://www.uvui.cn/components/dropDown.html" target="_blank">查看文档</a>

## [下载完整示例项目](https://ext.dcloud.net.cn/plugin?name=uv-ui) <small>（请不要 下载插件ZIP）</small>

### [更多插件，请关注uv-ui组件库](https://ext.dcloud.net.cn/plugin?name=uv-ui)

<a href="https://ext.dcloud.net.cn/plugin?name=uv-ui" target="_blank">

![image](https://mp-a667b617-c5f1-4a2d-9a54-683a67cff588.cdn.bspapp.com/uv-ui/banner.png)

</a>

#### 如使用过程中有任何问题反馈，或者您对uv-ui有一些好的建议，欢迎加入uv-ui官方交流群：<a href="https://www.uvui.cn/components/addQQGroup.html" target="_blank">官方QQ群</a>