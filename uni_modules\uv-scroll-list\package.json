{"id": "uv-scroll-list", "displayName": "uv-scroll-list 横向滚动列表 全面兼容vue3+2、app、h5、小程序等多端", "version": "1.0.5", "description": "该组件一般用于同时展示多个商品、分类的场景，也可以完成左右滑动的列表，往往数量不确定，数量较多支持左右滚动。灵活配置，开箱即用。", "keywords": ["uv-scroll-list", "uvui", "uv-ui", "横向滚动列表", "scroll-view"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["uv-ui-tools"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}