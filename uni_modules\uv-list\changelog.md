## 1.0.9（2023-11-10）
1. 修复设置ellipsis不生效的BUG
## 1.0.8（2023-09-20）
1. listItem优化可使用customStyle变量进行样式控制
## 1.0.7（2023-08-29）
1. 修复边框的BUG
## 1.0.6（2023-08-16）
1. 修复switch开关返回undefined的问题
2. 优化初始化可能导致的闪动
## 1.0.5（2023-08-07）
1. 修复分包页面在ios端，nvue编译不能滚动的BUG
## 1.0.4（2023-08-04）
1. nvue修复  触底不触发事件的BUG
2. 更新文档说明事件触发
## 1.0.3（2023-07-28）
1. 修改可能造成样式污染的BUG
## 1.0.2（2023-07-26）
1. 全面重构，用法与之前保持一致，参数全部变化
2. 新增多个功能参数，方便一键构建列表
3. List列表组件，包含基本列表样式、默认插槽机制、可扩展插槽机制、长列表性能优化、多端兼容。
## 1.0.1（2023-05-16）
1. 优化组件依赖，修改后无需全局引入，组件导入即可使用
2. 优化部分功能
## 1.0.0（2023-05-10）
uv-list 列表
