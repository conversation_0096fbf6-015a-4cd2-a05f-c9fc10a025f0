{"id": "uv-skeletons", "displayName": "uv-skeletons 骨架屏 全面兼容小程序、nvue、vue2、vue3等多端", "version": "1.0.1", "description": "全新升级骨架屏，更加灵活，体验更加，强烈推荐使用新版骨架屏。一般用于页面在请求远程数据尚未完成时，在内容加载出来前展示与内容布局结构一致的灰白块，提升用户视觉体验。", "keywords": ["uv-skeletons", "uvui", "uv-ui", "skeleton", "骨架屏"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["uv-ui-tools", "uv-text"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}