<template>
  <view class="responsive-container" :class="containerClass">
    <slot></slot>
  </view>
</template>

<script>
/**
 * 响应式容器组件
 * 提供响应式的内容容器，自动适配不同屏幕尺寸
 */
export default {
  name: 'ResponsiveContainer',
  props: {
    // 容器类型：fluid(流体宽度) | fixed(固定最大宽度)
    type: {
      type: String,
      default: 'fixed',
      validator: value => ['fluid', 'fixed'].includes(value)
    },
    // 是否添加内边距
    padding: {
      type: Boolean,
      default: true
    },
    // 自定义最大宽度
    maxWidth: {
      type: String,
      default: ''
    }
  },
  computed: {
    containerClass() {
      return {
        'container-fluid': this.type === 'fluid',
        'container-fixed': this.type === 'fixed',
        'no-padding': !this.padding
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.responsive-container {
  width: 100%;
  margin: 0 auto;
  height: 100%;
  overflow-y: auto;
  
  // 固定宽度容器
  &.container-fixed {
    @include responsive-container;
  }
  
  // 流体宽度容器
  &.container-fluid {
    max-width: 100%;
    padding: 0 $spacing-base;
    
    @include tablet-up {
      padding: 0 $spacing-lg;
    }
    
    @include desktop-up {
      padding: 0 $spacing-xl;
    }
  }
  
  // 无内边距
  &.no-padding {
    padding: 0 !important;
  }
}
</style>
