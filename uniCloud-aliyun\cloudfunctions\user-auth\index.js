'use strict'
const db = uniCloud.database()
const dbCmd = db.command
const crypto = require('crypto')

/**
 * 验证token并获取用户ID
 */
async function verifyToken(token) {
  if (!token) {
    return null
  }
  
  try {
    // 查找token对应的用户
    const userResult = await db.collection('uni-id-users')
      .where({
        'token.token': token,
        'token.expire': dbCmd.gt(new Date()),
        status: 0
      })
      .get()
    
    if (userResult.data.length > 0) {
      return userResult.data[0]._id
    }
    
    return null
  } catch (error) {
    console.error('验证token失败:', error)
    return null
  }
}

/**
 * 用户认证云函数
 * 提供登录、登出、获取用户信息等功能
 */
exports.main = async (event, context) => {
  const { action, data } = event
  
  try {
    switch (action) {
      case 'login':
        return await login(data)


      default:
        return {
          code: -1,
          message: '不支持的操作'
        }
    }
  } catch (error) {
    console.error('用户认证云函数执行失败:', error)
    return {
      code: -1,
      message: error.message || '操作失败'
    }
  }
}

/**
 * 用户登录
 */
async function login(loginData) {
  try {
    const { mobile, password } = loginData
    
    if (!mobile || !password) {
      return {
        code: -1,
        message: '手机号和密码不能为空'
      }
    }
    
    // 查找用户
    const userResult = await db.collection('uni-id-users')
      .where({
        mobile: mobile,
        status: 0 // 正常状态
      })
      .get()
    
    if (userResult.data.length === 0) {
      return {
        code: -1,
        message: '用户不存在或已被禁用'
      }
    }
    
    const user = userResult.data[0]
    
    // 验证密码
    const isPasswordValid = await verifyPassword(password, user.password)
    if (!isPasswordValid) {
      return {
        code: -1,
        message: '密码错误'
      }
    }
    
    // 生成token
    const token = generateToken(user._id)
    const tokenExpireTime = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后过期
    
    // 更新用户token和登录信息
    await db.collection('uni-id-users')
      .doc(user._id)
      .update({
        token: [{ 
          token: token,
          expire: tokenExpireTime 
        }],
        last_login_date: new Date(),
        last_login_ip: getClientIP(),
        status: 0
      })
    
    // 记录登录日志
    await db.collection('uni-id-log').add({
      user_id: user._id,
      username: user.username,
      mobile: user.mobile,
      type: 'login',
      state: 1,
      ip: getClientIP(),
      ua: getClientUA(),
      create_date: new Date()
    })
    
    return {
      code: 0,
      message: '登录成功',
      data: {
        token: token,
        userInfo: {
          uid: user._id,
          username: user.username,
          nickname: user.nickname,
          mobile: user.mobile,
          avatar: user.avatar,
          status: user.status
        }
      }
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      code: -1,
      message: '登录失败'
    }
  }
}





/**
 * 验证密码
 */
async function verifyPassword(inputPassword, storedPassword) {
  try {
    // 简单的密码验证，实际应用中应该使用加密算法
    // 这里假设存储的是明文密码，实际应用中应该存储哈希值
    return inputPassword === storedPassword
  } catch (error) {
    console.error('密码验证失败:', error)
    return false
  }
}

/**
 * 生成token
 */
function generateToken(userId) {
  const timestamp = Date.now().toString()
  const random = Math.random().toString(36).substring(2)
  const data = userId + timestamp + random
  return crypto.createHash('md5').update(data).digest('hex')
}

/**
 * 获取客户端IP
 */
function getClientIP() {
  try {
    return uniCloud.getClientInfo().clientIP || '0.0.0.0'
  } catch (error) {
    return '0.0.0.0'
  }
}

/**
 * 获取客户端UA
 */
function getClientUA() {
  try {
    return uniCloud.getClientInfo().userAgent || 'unknown'
  } catch (error) {
    return 'unknown'
  }
} 