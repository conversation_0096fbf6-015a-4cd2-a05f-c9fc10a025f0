'use strict'

/**
 * 日志级别常量定义
 */
const LOG_LEVELS = {
  SUCCESS: 1,    // 成功
  ERROR: 0,      // 失败
  INFO: 2,       // 信息
  WARNING: 3     // 警告
}

/**
 * 日志记录器类
 *
 * 负责统一管理系统操作日志的记录、格式化和存储
 * 支持单条日志记录、批量日志记录和不同级别的日志
 */
class Logger {
  constructor() {
    this.db = uniCloud.database()
    this.collectionName = 'operation-logs'
  }

  /**
   * 记录操作日志
   *
   * @param {Object} logData - 日志数据对象
   * @param {string} logData.user_id - 用户ID
   * @param {string} logData.platform_type - 平台类型
   * @param {string} logData.platform_shelf_id - 平台货架ID
   * @param {string} logData.game_account - 游戏账号
   * @param {string} logData.action - 操作类型
   * @param {number} logData.status - 操作状态
   * @param {string} logData.message - 操作消息
   * @param {string} logData.error_code - 错误代码
   * @param {Object} logData.request_data - 请求数据
   * @param {Object} logData.response_data - 响应数据
   * @param {number} logData.execution_time - 执行时间
   * @param {string} logData.trigger_type - 触发类型
   */
  async log(logData) {
    try {
      // {{ AURA-X: Modify - 使用统一的日志记录格式化方法. Approval: 寸止(ID:**********). }}
      const logRecord = this._formatLogRecord(logData)

      await this.db.collection(this.collectionName).add(logRecord)

      // 在控制台输出格式化的日志信息
      this._outputConsoleLog(logRecord)

    } catch (error) {
      console.error('记录日志失败:', error)
      // 日志记录失败不应该影响主流程，所以这里只打印错误不抛出异常
    }
  }

  /**
   * 记录成功日志
   * @param {Object} logData - 日志数据
   */
  async logSuccess(logData) {
    await this.log({
      ...logData,
      status: LOG_LEVELS.SUCCESS
    })
  }

  /**
   * 记录失败日志
   * @param {Object} logData - 日志数据
   */
  async logError(logData) {
    await this.log({
      ...logData,
      status: LOG_LEVELS.ERROR
    })
  }

  /**
   * 记录信息日志
   * @param {Object} logData - 日志数据
   */
  async logInfo(logData) {
    await this.log({
      ...logData,
      status: LOG_LEVELS.INFO
    })
  }

  /**
   * 记录警告日志
   * @param {Object} logData - 日志数据
   */
  async logWarning(logData) {
    await this.log({
      ...logData,
      status: LOG_LEVELS.WARNING
    })
  }

  /**
   * 批量记录日志
   * @param {Array} logDataArray - 日志数据数组
   */
  async logBatch(logDataArray) {
    try {
      // {{ AURA-X: Modify - 使用统一的日志格式化方法处理批量日志. Approval: 寸止(ID:**********). }}
      const logRecords = logDataArray.map(logData => this._formatLogRecord(logData))

      if (logRecords.length > 0) {
        await this.db.collection(this.collectionName).add(logRecords)
        console.log(`批量记录日志成功，共 ${logRecords.length} 条`)
      }
    } catch (error) {
      console.error('批量记录日志失败:', error)
    }
  }

  /**
   * 格式化日志记录（私有方法）
   *
   * 将原始日志数据转换为标准的数据库记录格式
   *
   * @param {Object} logData - 原始日志数据
   * @returns {Object} 格式化后的日志记录
   */
  _formatLogRecord(logData) {
    // {{ AURA-X: Add - 提取日志格式化逻辑，消除重复代码. Approval: 寸止(ID:**********). }}
    return {
      user_id: logData.user_id || '',
      platform_type: logData.platform_type || '',
      platform_shelf_id: logData.platform_shelf_id || '',
      game_account: logData.game_account || '',
      action: logData.action || '',
      status: logData.status || LOG_LEVELS.ERROR,
      message: logData.message || '',
      error_code: logData.error_code || '',
      request_data: logData.request_data || null,
      response_data: logData.response_data || null,
      execution_time: logData.execution_time || 0,
      trigger_type: logData.trigger_type || 'auto',
      create_time: new Date()
    }
  }

  /**
   * 输出控制台日志（私有方法）
   *
   * 根据日志级别输出不同格式的控制台信息
   *
   * @param {Object} logRecord - 格式化后的日志记录
   */
  _outputConsoleLog(logRecord) {
    // {{ AURA-X: Add - 提取控制台日志输出逻辑，支持不同级别. Approval: 寸止(ID:**********). }}
    const levelText = this._getLogLevelText(logRecord.status)
    const logMessage = `[${levelText}] [${logRecord.action}] ${logRecord.platform_type} - ${logRecord.message}`

    // 根据日志级别使用不同的控制台输出方法
    switch (logRecord.status) {
      case LOG_LEVELS.ERROR:
        console.error(logMessage)
        break
      case LOG_LEVELS.WARNING:
        console.warn(logMessage)
        break
      case LOG_LEVELS.INFO:
        console.info(logMessage)
        break
      case LOG_LEVELS.SUCCESS:
      default:
        console.log(logMessage)
        break
    }
  }

  /**
   * 获取日志级别文本（私有方法）
   *
   * @param {number} level - 日志级别
   * @returns {string} 级别文本
   */
  _getLogLevelText(level) {
    // {{ AURA-X: Add - 日志级别文本映射. Approval: 寸止(ID:**********). }}
    const levelTexts = {
      [LOG_LEVELS.SUCCESS]: '成功',
      [LOG_LEVELS.ERROR]: '错误',
      [LOG_LEVELS.INFO]: '信息',
      [LOG_LEVELS.WARNING]: '警告'
    }

    return levelTexts[level] || '未知'
  }
}

module.exports = Logger
