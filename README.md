# 多平台租号货架监测系统

基于 uniapp（Vue2）+ uniCloud 云开发的多平台租号货架监测系统，统一管理游戏账号在多个租号平台的上下架状态。

## 📌 系统特色

- **统一管理多个租号平台的账号上下架状态**
- **避免重复出租，提升账号利用率**
- **全部第三方平台接口调用操作均在服务端（云函数）完成**
- **客户端无需挂机，仅用于账号配置与状态查看**
- **支持自动登录和手动配置Cookie两种登录方式**
- **实时监控货架状态，自动联动上下架**

## 🔧 技术栈

- **前端框架：** uniapp（Vue2）
- **UI 框架：** uv-ui（移动端适配）
- **云开发平台：** uniCloud
- **数据库：** uniCloud 云数据库
- **定时任务：** 云函数定时触发器（每分钟执行）

## 🎯 核心功能

### 1. 多平台上下架联动
- 当账号在平台 A 出租时，自动调用平台 B、C 的下架接口
- 出租结束后，自动重新上架至所有平台
- 支持平台：租号玩、U号租（可扩展）

### 2. 智能登录策略
- **自动登录：** 支持账号密码登录的平台，云函数自动获取并存储 token
- **手动配置：** 不支持自动登录的平台，客户端手动配置 Cookie
- **状态检测：** 自动检测登录状态，失效时自动重新登录

### 3. 平台字段统一
- 统一各平台的字段差异（账号、状态等）
- 云函数中建立字段映射和状态解析逻辑
- 支持平台状态的标准化处理

### 4. 定时更新机制
- 云函数每分钟自动轮询所有平台账号货架状态
- 对比当前出租状态，自动处理上下架逻辑
- 支持手动触发更新和单独操作货架

## 📱 客户端功能

### 货架监控页面
- 显示平台配置统计、货架状态统计
- 实时查看各平台登录状态
- 快速操作：立即更新、平台配置、货架管理

### 平台配置页面
- 添加/编辑平台配置
- 支持账号密码和Cookie两种配置方式
- 平台登录功能

### 货架管理页面
- 查看所有货架状态
- 支持筛选和搜索
- 手动上下架操作

### 操作日志页面
- 查看所有操作记录
- 支持按平台、操作类型筛选
- 实时显示操作结果

## 🚀 部署说明

### 1. 环境要求
- HBuilderX 3.0+
- uniCloud 阿里云版
- Node.js 14+

### 2. 项目配置
```bash
# 安装依赖
npm install

# 配置uniCloud
# 在HBuilderX中关联uniCloud项目
```

### 3. 云函数部署
```bash
# 上传云函数
- shelf-monitor（监控云函数）
- shelf-management（管理云函数）

# 配置定时触发器
- shelf-monitor 配置为每分钟执行一次
```

### 4. 数据库初始化
```bash
# 创建数据库表
- platform-configs（平台配置表）
- account-shelves（账号货架表）
- operation-logs（操作日志表）
```

## 📊 数据库设计

### platform-configs（平台配置表）
```json
{
  "user_id": "用户ID",
  "platform_name": "平台名称",
  "platform_type": "平台类型标识",
  "username": "登录用户名",
  "password": "登录密码",
  "token": "访问令牌",
  "cookie": "Cookie信息",
  "auto_login": "是否支持自动登录",
  "login_status": "登录状态",
  "status": "配置状态"
}
```

### account-shelves（账号货架表）
```json
{
  "user_id": "用户ID",
  "platform_type": "平台类型",
  "platform_shelf_id": "平台货架ID",
  "game_account": "游戏账号名",
  "game_name": "游戏名称",
  "shelf_title": "货架标题",
  "rent_price": "租赁价格",
  "status": "货架状态",
  "rent_status": "租赁状态",
  "is_active": "是否启用监控"
}
```

### operation-logs（操作日志表）
```json
{
  "user_id": "用户ID",
  "platform_type": "平台类型",
  "action": "操作类型",
  "status": "操作状态",
  "message": "操作消息",
  "trigger_type": "触发类型",
  "create_time": "创建时间"
}
```

## 🔌 平台接口

### 租号玩 API
- 登录接口：`/api/Login/login`
- 获取货架：`/api/Account/search`
- 上架接口：`/api/Account/onRent`
- 下架接口：`/api/Account/offRent`
### 租号玩登录过期
```json
{
    "code": 401,
    "status": 401,
    "data": null,
    "message": "登录已过期，请重新登录【113】"
}
```

### U号租 API
- 登录接口：`/tool/user/access-token`
- 获取货架：`/tool/goods/list/all`
- 上下架接口：`/tool/goods/status/update`
### U号租登录过期
```json
{
    "success": false,
    "responseCode": "4024",
    "responseMsg": "无权限",
    "object": ""
}
```

## 🔄 系统工作流程

1. **初始化配置**
   - 用户在客户端配置各平台的登录信息
   - 系统验证配置并存储到数据库

2. **定时更新**
   - 云函数每分钟自动执行
   - 检查所有平台的登录状态
   - 获取各平台的货架状态

3. **状态分析**
   - 对比各平台同一账号的状态
   - 检测是否有账号出租

4. **联动操作**
   - 发现出租账号时，自动下架其他平台
   - 出租结束时，自动重新上架

5. **日志记录**
   - 记录所有操作到日志表
   - 客户端可查看操作历史

## 🛠 扩展新平台

### 1. 创建平台适配器
```javascript
// 新建 lib/adapters/newplatform-adapter.js
class NewPlatformAdapter extends BaseAdapter {
  async login() {
    // 实现登录逻辑
  }
  
  async getShelfList() {
    // 实现获取货架列表
  }
  
  async onShelf(shelfId) {
    // 实现上架逻辑
  }
  
  async offShelf(shelfId) {
    // 实现下架逻辑
  }
}
```

### 2. 注册到工厂类
```javascript
// lib/platform-adapter-factory.js
case 'newplatform':
  return new NewPlatformAdapter(config);
```

### 3. 更新支持列表
```javascript
// 添加到 getSupportedPlatforms 方法
{
  type: 'newplatform',
  name: '新平台',
  autoLogin: true,
  description: '支持自动登录'
}
```

## 📝 注意事项

1. **安全性**
   - 密码等敏感信息建议加密存储
   - API调用时注意频率限制
   - 定期检查token有效性

2. **稳定性**
   - 网络异常时的重试机制
   - 平台API变更时的兼容处理
   - 日志文件的定期清理

3. **性能优化**
   - 合理设置更新频率
   - 批量处理数据库操作
   - 缓存常用数据

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 技术交流群

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。
