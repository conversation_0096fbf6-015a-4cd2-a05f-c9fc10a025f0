<template>
  <responsive-layout
    :page-title="'账号管理'"
  >
    <responsive-container>
      <!-- 搜索和筛选 -->
      <view class="search-filter">
        <view class="search-box">
          <uv-input
            v-model="searchKeyword"
            placeholder="搜索游戏账号"
            border="none"
            prefixIcon="search"
            prefixIconStyle="color: #999; font-size: 32rpx;"
            :customStyle="{
              backgroundColor: '#f5f5f5',
              borderRadius: '24rpx',
              padding: '12rpx 20rpx'
            }"
            @input="onSearchInput"
          />
        </view>
        <!-- 平台筛选 -->
        <view class="platform-tabs">
          <view
            class="filter-tab"
            :class="{ active: selectedPlatform === 'all' }"
            @click="onPlatformChange('all')"
          >
            全部平台
          </view>
          <view
            v-for="platform in platformList"
            :key="platform.platform_type"
            class="filter-tab"
            :class="{ active: selectedPlatform === platform.platform_type }"
            @click="onPlatformChange(platform.platform_type)"
          >
            {{ platform.platform_name }}
          </view>
        </view>
      </view>

      <!-- 统计信息 -->
      <responsive-grid
        :mobile-cols="4"
        :tablet-cols="4"
        :desktop-cols="4"
        gap="sm"
        class="stats"
      >
        <view class="stat-item grid-item">
          <text class="stat-value">{{ filteredList.length }}</text>
          <text class="stat-label">当前显示</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.monitoring }}</text>
          <text class="stat-label">监控中</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.totalShelves }}</text>
          <text class="stat-label">总货架数</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.activeShelves }}</text>
          <text class="stat-label">活跃货架</text>
        </view>
      </responsive-grid>

      <!-- 账号列表 -->
      <responsive-grid
        :mobile-cols="1"
        :tablet-cols="1"
        :desktop-cols="2"
        gap="base"
        class="account-list"
      >
        <view
          v-for="account in filteredList"
          :key="account.game_account"
          class="account-item grid-item"
        >
          <view class="account-header">
            <view class="account-info">
              <text class="account-name">{{ account.game_account }}</text>
              <view class="account-tags">
                <text class="platform-count-tag">{{ account.platforms.length }}个平台</text>
                <!-- {{ AURA-X: Modify - 优化监控状态显示，支持部分监控状态. Approval: 寸止(ID:**********). }} -->
                <text
                  class="monitor-tag"
                  :class="getMonitorTagClass(account)"
                >
                  {{ getMonitorStatusText(account) }}
                </text>
              </view>
            </view>
          </view>

          <!-- 平台分布 -->
          <view class="platform-distribution">
            <text class="section-title">平台分布</text>
            <view class="platform-list">
              <view
                v-for="platform in account.platforms"
                :key="platform.platform_type"
                class="platform-item"
              >
                <view class="platform-header">
                  <text class="platform-name">{{ platform.platform_name }}</text>
                  <text class="shelf-count">{{ platform.shelf_count }}个货架</text>
                </view>
                <view class="platform-stats">
                  <text class="stat-item">待租: {{ platform.states.available }}</text>
                  <text class="stat-item">出租: {{ platform.states.rented }}</text>
                  <text class="stat-item">下架: {{ platform.states.offline }}</text>
                </view>
                <!-- {{ AURA-X: Add - 显示平台中的货架标题列表. Approval: 寸止(ID:**********). }} -->
                <view v-if="platform.shelves && platform.shelves.length > 0" class="shelf-titles">
                  <text class="shelf-titles-label">货架列表：</text>
                  <view class="shelf-title-list">
                    <text
                      v-for="(shelf, index) in platform.shelves"
                      :key="shelf._id"
                      class="shelf-title-item"
                      :class="getShelfStateClass(shelf.unified_state)"
                    >
                      {{ shelf.shelf_title }}{{ index < platform.shelves.length - 1 ? '、' : '' }}
                    </text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="account-footer">
            <view class="monitor-switch">
              <text class="switch-label">监控状态：</text>
              <!-- {{ AURA-X: Modify - 优化监控开关逻辑，支持部分监控状态的处理. Approval: 寸止(ID:**********). }} -->
              <uv-switch
                :value="account.is_monitoring"
                size="40"
                @change="(value) => toggleAccountMonitor(account, value)"
                :loading="account.monitorLoading"
              ></uv-switch>
              <text
                v-if="account.partial_monitoring"
                class="partial-monitor-hint"
              >
                ({{ account.active_shelves }}/{{ account.total_shelves }}个启用)
              </text>
            </view>
            <!-- {{ AURA-X: Modify - 使用操作菜单组件优化按钮布局. Approval: 寸止(ID:**********). }} -->
            <view class="account-actions">
              <action-menu
                v-if="hasAnyAction(account)"
                :target-name="account.game_account"
                :actions="getAccountActions(account)"
                @action="(actionKey, actionData) => handleAccountAction(actionKey, actionData, account)"
              ></action-menu>
            </view>
          </view>
        </view>
      </responsive-grid>

      <!-- 空状态 -->
      <view v-if="filteredList.length === 0 && !loading" class="empty-state">
        <text class="empty-icon">👤</text>
        <text class="empty-text">{{ emptyStateText }}</text>
        <text class="empty-desc">{{ emptyStateDesc }}</text>
        <uv-button
          v-if="platformList.length === 0"
          type="primary"
          @click="goToConfig"
          :customStyle="{
            borderRadius: '24rpx',
            marginTop: '20rpx'
          }"
          text="前往配置"
        >
        </uv-button>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && !loading && accountList.length > 0" class="load-more">
        <uv-button
          type="info"
          :loading="isLoadingMore"
          @click="loadMore"
          :customStyle="{
            borderRadius: '20rpx'
          }"
          :text="isLoadingMore ? '加载中...' : '加载更多'"
        >
        </uv-button>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading">
        <uv-loading-icon mode="circle" size="40"></uv-loading-icon>
        <text style="margin-left: 20rpx;">加载中...</text>
      </view>
    </responsive-container>

    <!-- {{ AURA-X: Modify - 使用公共定时任务设置组件. Approval: 寸止(ID:**********). }} -->
    <schedule-task-dialog
      ref="scheduleDialog"
      :task-type="currentTaskType"
      target-type="account"
      :target-name="currentAccount && currentAccount.game_account"
      :target-id="currentAccount && currentAccount.game_account"
      @success="handleScheduleSuccess"
      @cancel="handleScheduleCancel"
    ></schedule-task-dialog>
  </responsive-layout>
</template>

<script>
import { callFunction } from '@/utils/request.js'
// {{ AURA-X: Add - 引入响应式布局组件. Approval: 寸止(ID:**********). }}
import ResponsiveLayout from '@/components/layout/responsive-layout.vue'
import ResponsiveContainer from '@/components/layout/responsive-container.vue'
import ResponsiveGrid from '@/components/layout/responsive-grid.vue'
// {{ AURA-X: Add - 引入公共工具函数，保持与现有页面一致. Approval: 寸止(ID:**********). }}
import utils from '@/common/js/utils.js'
// {{ AURA-X: Add - 引入公共定时任务设置组件. Approval: 寸止(ID:**********). }}
import ScheduleTaskDialog from '@/components/schedule/schedule-task-dialog.vue'
// {{ AURA-X: Add - 引入操作菜单组件，优化按钮布局. Approval: 寸止(ID:**********). }}
import ActionMenu from '@/components/common/action-menu.vue'

export default {
  components: {
    ResponsiveLayout,
    ResponsiveContainer,
    ResponsiveGrid,
    ScheduleTaskDialog,
    ActionMenu
  },
  name: 'AccountManagement',
  data() {
    return {
      accountList: [],
      platformList: [],
      searchKeyword: '',
      selectedPlatform: 'all',
      loading: false,
      isLoadingMore: false,
      hasMore: true,
      currentPage: utils.getConstant('DEFAULT_CURRENT_PAGE'),
      pageSize: utils.getConstant('DEFAULT_PAGE_SIZE'),
      currentTabIndex: 2, // 账号管理页面是第3个tab，索引为2

      // {{ AURA-X: Modify - 简化定时任务相关数据，使用公共组件. Approval: 寸止(ID:**********). }}
      currentAccount: null,
      currentTaskType: ''
    }
  },
  computed: {
    filteredList() {
      let list = this.accountList

      // 搜索过滤
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.trim().toLowerCase()
        list = list.filter(account =>
          account.game_account.toLowerCase().includes(keyword)
        )
      }

      return list
    },
    
    totalStats() {
      const stats = {
        monitoring: 0,
        totalShelves: 0,
        activeShelves: 0
      }
      
      this.accountList.forEach(account => {
        if (account.is_monitoring) {
          stats.monitoring++
        }
        stats.totalShelves += account.total_shelves
        stats.activeShelves += account.active_shelves
      })
      
      return stats
    },

    emptyStateText() {
      if (this.platformList.length === 0) {
        return '暂无平台配置'
      }
      if (this.accountList.length === 0) {
        return '暂无账号数据'
      }
      return '暂无符合条件的账号'
    },

    emptyStateDesc() {
      if (this.platformList.length === 0) {
        return '请先配置平台账号信息'
      }
      if (this.accountList.length === 0) {
        return '请先更新平台货架数据'
      }
      return '请尝试调整筛选条件'
    },


  },
  
  async onShow() {
    await this.loadPlatformList()
    this.loadAccountList()
  },

  onReachBottom() {
    this.loadMore()
  },

  methods: {
    async loadPlatformList() {
      try {
        const result = await callFunction('shelf-management', {
          action: 'getPlatformList'
        })
        if (result.code === 0) {
          this.platformList = result.data.map(platform => ({
            platform_type: platform.type,
            platform_name: platform.name
          })) || []
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('加载平台列表失败:', error)
        this.platformList = [
          { platform_type: 'zuhaowan', platform_name: '租号玩' },
          { platform_type: 'uhaozu', platform_name: 'U号租' }
        ]
        utils.showError('平台列表加载失败')
      }
    },

    async loadAccountList(isLoadMore = false) {
      if (isLoadMore) {
        this.isLoadingMore = true
      } else {
        this.loading = true
        this.currentPage = 1
        this.hasMore = true
      }

      try {
        const requestData = {
          pageIndex: this.currentPage,
          pageSize: this.pageSize
        }

        // 如果选择了特定平台，添加平台筛选参数
        if (this.selectedPlatform !== 'all') {
          requestData.platformType = this.selectedPlatform
        }

        // 添加搜索关键词
        if (this.searchKeyword.trim()) {
          requestData.searchKeyword = this.searchKeyword.trim()
        }

        const result = await callFunction('shelf-management', {
          action: 'getAccountList',
          data: requestData
        })

        if (result.code === 0) {
          const newAccounts = result.data.list.map(account => ({
            ...account,
            batchOffLoading: false,
            batchOnLoading: false,
            monitorLoading: false
          }))

          if (isLoadMore) {
            this.accountList = [...this.accountList, ...newAccounts]
          } else {
            this.accountList = newAccounts
          }

          // 判断是否还有更多数据
          this.hasMore = newAccounts.length === this.pageSize
          if (this.hasMore) {
            this.currentPage++
          }
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('加载账号列表失败:', error)
        utils.showError('加载失败')
      } finally {
        this.loading = false
        this.isLoadingMore = false
      }
    },

    loadMore() {
      if (this.hasMore && !this.loading && !this.isLoadingMore) {
        this.loadAccountList(true)
      }
    },

    async toggleAccountMonitor(account, value) {
      account.monitorLoading = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'toggleAccountMonitor',
          data: {
            gameAccount: account.game_account,
            isActive: value
          }
        })

        if (result.code === 0) {
          // {{ AURA-X: Modify - 优化监控状态更新逻辑. Approval: 寸止(ID:**********). }}
          // 更新活跃货架数量
          if (value) {
            account.active_shelves = account.total_shelves
            account.is_monitoring = true
            account.partial_monitoring = false
          } else {
            account.active_shelves = 0
            account.is_monitoring = false
            account.partial_monitoring = false
          }
          utils.showSuccess(result.message)
        } else {
          utils.showError(result.message)
        }
      } catch (error) {
        console.error('切换账号监控状态失败:', error)
        utils.showError('操作失败')
      } finally {
        account.monitorLoading = false
      }
    },

    async batchOffShelf(account) {
      // 确认操作
      const confirmed = await new Promise((resolve) => {
        uni.showModal({
          title: '确认批量下架',
          content: `确定要下架账号"${account.game_account}"在所有平台的货架吗？`,
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        })
      })

      if (!confirmed) return

      account.batchOffLoading = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'batchOffShelfByAccount',
          data: {
            gameAccount: account.game_account
          }
        })

        if (result.code === 0) {
          utils.showSuccess(result.message)
          // 刷新账号列表以获取最新状态
          this.loadAccountList()
        } else {
          utils.showError(result.message)
        }
      } catch (error) {
        console.error('批量下架失败:', error)
        utils.showError('操作失败')
      } finally {
        account.batchOffLoading = false
      }
    },

    async batchOnShelf(account) {
      // 确认操作
      const confirmed = await new Promise((resolve) => {
        uni.showModal({
          title: '确认批量上架',
          content: `确定要上架账号"${account.game_account}"在所有平台的下架货架吗？`,
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        })
      })

      if (!confirmed) return

      account.batchOnLoading = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'batchOnShelfByAccount',
          data: {
            gameAccount: account.game_account
          }
        })

        if (result.code === 0) {
          utils.showSuccess(result.message)
          // 刷新账号列表以获取最新状态
          this.loadAccountList()
        } else {
          utils.showError(result.message)
        }
      } catch (error) {
        console.error('批量上架失败:', error)
        utils.showError('操作失败')
      } finally {
        account.batchOnLoading = false
      }
    },

    onSearchInput() {
      // 搜索输入防抖处理
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.loadAccountList()
      }, 300)
    },

    onPlatformChange(platformType) {
      this.selectedPlatform = platformType
      this.loadAccountList() // 重新加载数据
    },

    goToConfig() {
      uni.navigateTo({
        url: '/pages/platform-config/index'
      })
    },

    // {{ AURA-X: Add - 获取货架状态样式类. Approval: 寸止(ID:**********). }}
    getShelfStateClass(state) {
      const stateMap = {
        0: 'available',   // 待租
        1: 'rented',      // 出租中
        [-1]: 'offline',  // 已下架
        [-2]: 'unknown'   // 其他
      }
      return stateMap[state] || 'unknown'
    },

    // {{ AURA-X: Add - 检查是否可以批量上架. Approval: 寸止(ID:**********). }}
    canBatchOnShelf(account) {
      // 如果有出租中的货架，不能立即批量上架
      if (account.states.rented > 0) {
        return false
      }
      // 如果有已下架的货架，可以上架
      return account.states.offline > 0
    },

    // {{ AURA-X: Add - 检查是否可以批量下架. Approval: 寸止(ID:**********). }}
    canBatchOffShelf(account) {
      // 如果有出租中的货架，不能立即批量下架
      if (account.states.rented > 0) {
        return false
      }
      // 如果有待租的货架，可以下架
      return account.states.available > 0
    },

    // {{ AURA-X: Add - 获取监控状态文本. Approval: 寸止(ID:**********). }}
    getMonitorStatusText(account) {
      if (account.is_monitoring) {
        return '全部监控'
      } else if (account.partial_monitoring) {
        return '部分监控'
      } else {
        return '未监控'
      }
    },

    // {{ AURA-X: Add - 获取监控标签样式类. Approval: 寸止(ID:**********). }}
    getMonitorTagClass(account) {
      if (account.is_monitoring) {
        return 'active'
      } else if (account.partial_monitoring) {
        return 'partial'
      } else {
        return ''
      }
    },

    // {{ AURA-X: Add - 操作菜单相关方法. Approval: 寸止(ID:**********). }}

    // 获取账号可用操作列表
    getAccountActions(account) {
      const actions = []

      // {{ AURA-X: Modify - 有出租中货架的账号只能操作定时下架. Approval: 寸止(ID:**********). }}
      // 如果有出租中的货架，只能进行定时下架操作
      if (account.states.rented > 0) {
        actions.push({
          key: 'schedule_off_shelf',
          title: '定时下架',
          desc: `设置 ${account.states.rented} 个出租中货架的定时下架`,
          icon: '⏰',
          iconClass: 'error',
          loading: account.scheduleOffLoading,
          disabled: account.scheduleOffLoading
        })
        return actions  // 有出租中货架时，只返回定时下架操作
      }

      // 没有出租中货架时，可以进行其他操作

      // 批量上架操作
      if (this.canBatchOnShelf(account)) {
        actions.push({
          key: 'batch_on_shelf',
          title: '批量上架',
          desc: `上架 ${account.states.offline} 个下架货架`,
          icon: '⬆️',
          iconClass: 'success',
          loading: account.batchOnLoading,
          disabled: account.batchOnLoading
        })
      }

      // 批量下架操作
      if (this.canBatchOffShelf(account)) {
        actions.push({
          key: 'batch_off_shelf',
          title: '批量下架',
          desc: `下架 ${account.states.available} 个待租货架`,
          icon: '⬇️',
          iconClass: 'warning',
          loading: account.batchOffLoading,
          disabled: account.batchOffLoading
        })
      }

      // 定时上架操作
      if (account.states.offline > 0) {
        actions.push({
          key: 'schedule_on_shelf',
          title: '定时上架',
          desc: '设置批量自动上架时间',
          icon: '⏰',
          iconClass: 'primary',
          loading: account.scheduleOnLoading,
          disabled: account.scheduleOnLoading
        })
      }

      // 定时下架操作
      if (account.states.available > 0) {
        actions.push({
          key: 'schedule_off_shelf',
          title: '定时下架',
          desc: '设置批量自动下架时间',
          icon: '⏰',
          iconClass: 'error',
          loading: account.scheduleOffLoading,
          disabled: account.scheduleOffLoading
        })
      }

      return actions
    },

    // 处理账号操作
    handleAccountAction(actionKey, actionData, account) {
      // {{ AURA-X: Fix - 修复操作处理逻辑，直接传递账号对象. Approval: 寸止(ID:**********). }}
      if (!account) {
        console.error('账号对象为空')
        return
      }

      switch (actionKey) {
        case 'batch_on_shelf':
          this.batchOnShelf(account)
          break
        case 'batch_off_shelf':
          this.batchOffShelf(account)
          break
        case 'schedule_on_shelf':
          this.showScheduleDialog(account, 'on_shelf')
          break
        case 'schedule_off_shelf':
          this.showScheduleDialog(account, 'off_shelf')
          break
      }
    },

    // {{ AURA-X: Modify - 简化操作判断逻辑，有出租中货架只能定时下架. Approval: 寸止(ID:**********). }}

    // 判断是否有任何可操作的按钮
    hasAnyAction(account) {
      // 有出租中货架时，只能定时下架
      if (account.states.rented > 0) {
        return true
      }

      // 没有出租中货架时，检查其他操作
      return this.canBatchOnShelf(account) ||
             this.canBatchOffShelf(account) ||
             account.states.offline > 0 ||  // 可以定时上架
             account.states.available > 0   // 可以定时下架
    },

    // {{ AURA-X: Modify - 使用公共组件简化定时任务处理. Approval: 寸止(ID:**********). }}

    // 显示定时任务设置弹窗
    showScheduleDialog(account, taskType) {
      this.currentAccount = account
      this.currentTaskType = taskType
      this.$refs.scheduleDialog.open()
    },

    // 处理定时任务成功
    handleScheduleSuccess() {
      // 可以在这里刷新数据或显示成功提示
      this.handleScheduleCancel()
    },

    // 处理定时任务取消
    handleScheduleCancel() {
      this.currentAccount = null
      this.currentTaskType = ''
    },


  }
}
</script>

<style scoped lang="scss">
// {{ AURA-X: Add - 复用货架页面样式基础，添加账号管理特有样式. Approval: 寸止(ID:**********). }}

// 基础样式复用
.search-filter {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-base;
  box-shadow: $shadow-sm;
}

.search-box {
  margin-bottom: $spacing-base;
}

.platform-tabs {
  display: flex;
  gap: $spacing-sm;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 8rpx $spacing-sm;
  border-radius: $border-radius-pill;
  font-size: $font-size-sm;
  color: $text-color-secondary;
  background: $bg-color-overlay;
  border: 1rpx solid $border-color-light;
  transition: all $transition-fast;

  &.active {
    background: $primary-color;
    color: white;
    border-color: $primary-color;
  }

  &:active {
    opacity: 0.8;
  }
}

.stats {
  display: flex;
  gap: $spacing-sm;
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: $font-size-xxl;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  font-weight: $font-weight-normal;
}

// 账号管理特有样式
.account-item {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-base;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-base;
}

.account-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  margin-bottom: 12rpx;
}

.account-tags {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-wrap: wrap;
}

.platform-count-tag {
  font-size: $font-size-xs;
  color: $info-color;
  background-color: $info-light;
  padding: 4rpx 8rpx;
  border-radius: $border-radius-xs;
  font-weight: $font-weight-medium;
}

.monitor-tag {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;
  background-color: $error-light;
  color: $error-color;

  &.active {
    background-color: $success-light;
    color: $success-color;
  }

  // {{ AURA-X: Add - 部分监控状态样式. Approval: 寸止(ID:**********). }}
  &.partial {
    background-color: $warning-light;
    color: $warning-color;
  }
}

.platform-distribution {
  margin-bottom: $spacing-base;
}

.section-title {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-bottom: $spacing-sm;
  display: block;
  font-weight: $font-weight-medium;
}

.platform-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.platform-item {
  background: $bg-color-overlay;
  border-radius: $border-radius-base;
  padding: $spacing-sm;
  border: 1rpx solid $border-color-light;
}

.platform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.platform-name {
  font-size: $font-size-sm;
  color: $text-color-primary;
  font-weight: $font-weight-medium;
}

.shelf-count {
  font-size: $font-size-xs;
  color: $text-color-secondary;
}

.platform-stats {
  display: flex;
  gap: $spacing-sm;
  flex-wrap: wrap;
}

.platform-stats .stat-item {
  font-size: $font-size-xs;
  color: $text-color-secondary;
  flex: none;
}

// {{ AURA-X: Add - 货架标题列表样式. Approval: 寸止(ID:**********). }}
.shelf-titles {
  margin-top: $spacing-sm;
}

.shelf-titles-label {
  font-size: $font-size-xs;
  color: $text-color-secondary;
  margin-bottom: 8rpx;
  display: block;
}

.shelf-title-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4rpx;
}

.shelf-title-item {
  font-size: $font-size-xs;
  padding: 2rpx 6rpx;
  border-radius: $border-radius-xs;
  display: inline-block;

  &.available {
    background-color: $success-light;
    color: $success-color;
  }

  &.rented {
    background-color: $warning-light;
    color: $warning-color;
  }

  &.offline {
    background-color: $error-light;
    color: $error-color;
  }

  &.unknown {
    background-color: $info-light;
    color: $info-color;
  }
}

.account-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: $spacing-base;
  border-top: 1rpx solid $border-color-light;
}

.monitor-switch {
  display: flex;
  align-items: center;
}

.switch-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-right: $spacing-sm;
}

// {{ AURA-X: Add - 部分监控提示样式. Approval: 寸止(ID:**********). }}
.partial-monitor-hint {
  font-size: $font-size-xs;
  color: $warning-color;
  margin-left: $spacing-sm;
  font-style: italic;
}

// {{ AURA-X: Modify - 简化账号操作区域样式，使用操作菜单. Approval: 寸止(ID:**********). }}
.account-actions {
  margin-left: $spacing-base;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.empty-state {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: $spacing-lg;
  color: $text-color-placeholder;
}

.empty-text {
  font-size: $font-size-lg;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
  font-weight: $font-weight-medium;
}

.empty-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
  margin-bottom: $spacing-xl;
}

.load-more {
  text-align: center;
  padding: $spacing-base;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  color: $text-color-secondary;
}


</style>
