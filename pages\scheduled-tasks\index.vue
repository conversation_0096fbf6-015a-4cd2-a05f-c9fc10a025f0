<template>
  <responsive-layout :page-title="'定时任务'">
    <responsive-container>
      <!-- 筛选和统计 -->
      <view class="filter-section">
        <!-- 状态筛选 -->
        <view class="filter-tabs">
          <view
            v-for="tab in statusTabs"
            :key="tab.value"
            class="filter-tab"
            :class="{ active: currentStatus === tab.value }"
            @click="onStatusChange(tab.value)"
          >
            {{ tab.label }}
          </view>
        </view>
        
        <!-- 任务类型筛选 -->
        <view class="filter-tabs">
          <view
            v-for="tab in typeTabs"
            :key="tab.value"
            class="filter-tab"
            :class="{ active: currentType === tab.value }"
            @click="onTypeChange(tab.value)"
          >
            {{ tab.label }}
          </view>
        </view>
      </view>

      <!-- 统计信息 -->
      <responsive-grid
        :mobile-cols="4"
        :tablet-cols="4"
        :desktop-cols="4"
        gap="sm"
        class="stats"
      >
        <view class="stat-item grid-item">
          <text class="stat-value">{{ filteredList.length }}</text>
          <text class="stat-label">当前显示</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.pending }}</text>
          <text class="stat-label">待执行</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.completed }}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.failed }}</text>
          <text class="stat-label">失败</text>
        </view>
      </responsive-grid>

      <!-- 任务列表 -->
      <responsive-grid
        :mobile-cols="1"
        :tablet-cols="1"
        :desktop-cols="2"
        gap="base"
        class="task-list"
      >
        <view
          v-for="task in filteredList"
          :key="task._id"
          class="task-item grid-item"
        >
          <view class="task-header">
            <view class="task-info">
              <text class="task-title">{{ getTaskTitle(task) }}</text>
              <view class="task-tags">
                <text class="task-type-tag" :class="getTaskTypeClass(task.task_type)">
                  {{ getTaskTypeText(task.task_type) }}
                </text>
                <text class="task-status-tag" :class="getTaskStatusClass(task.status)">
                  {{ getTaskStatusText(task.status) }}
                </text>
              </view>
            </view>
          </view>
          
          <view class="task-content">
            <view class="task-detail">
              <text class="detail-label">目标：</text>
              <text class="detail-value">{{ task.target_id }}</text>
            </view>
            <view class="task-detail">
              <text class="detail-label">执行时间：</text>
              <text class="detail-value">{{ formatTime(task.execute_time) }}</text>
            </view>
            <view class="task-detail" v-if="task.description">
              <text class="detail-label">描述：</text>
              <text class="detail-value">{{ task.description }}</text>
            </view>
            <view class="task-detail" v-if="task.execution_result">
              <text class="detail-label">执行结果：</text>
              <text class="detail-value">
                成功{{ task.execution_result.success_count || 0 }}个，
                失败{{ task.execution_result.failed_count || 0 }}个
              </text>
            </view>
          </view>
          
          <view class="task-footer">
            <text class="create-time">{{ formatTime(task.create_time) }}</text>
            <view class="task-actions">
              <uv-button
                v-if="canCancelTask(task)"
                type="error"
                size="small"
                plain
                :loading="task.cancelling"
                @click="cancelTask(task)"
                :customStyle="{
                  borderRadius: '100px',
                  fontSize: '24rpx',
                  padding: '0 24rpx'
                }"
                text="取消"
              >
              </uv-button>
            </view>
          </view>
        </view>
      </responsive-grid>

      <!-- 空状态 -->
      <view v-if="filteredList.length === 0 && !loading" class="empty-state">
        <text class="empty-icon">⏰</text>
        <text class="empty-text">{{ emptyStateText }}</text>
        <text class="empty-desc">{{ emptyStateDesc }}</text>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && !loading && taskList.length > 0" class="load-more">
        <uv-button
          type="info"
          :loading="isLoadingMore"
          @click="loadMore"
          :customStyle="{
            borderRadius: '20rpx'
          }"
          :text="isLoadingMore ? '加载中...' : '加载更多'"
        >
        </uv-button>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading">
        <uv-loading-icon mode="circle" size="40"></uv-loading-icon>
        <text style="margin-left: 20rpx;">加载中...</text>
      </view>
    </responsive-container>
  </responsive-layout>
</template>

<script>
import { callFunction } from '@/utils/request.js'
import ResponsiveLayout from '@/components/layout/responsive-layout.vue'
import ResponsiveContainer from '@/components/layout/responsive-container.vue'
import ResponsiveGrid from '@/components/layout/responsive-grid.vue'
import utils from '@/common/js/utils.js'

export default {
  components: {
    ResponsiveLayout,
    ResponsiveContainer,
    ResponsiveGrid
  },
  name: 'ScheduledTasks',
  data() {
    return {
      taskList: [],
      currentStatus: 'all',
      currentType: 'all',
      loading: false,
      isLoadingMore: false,
      hasMore: true,
      currentPage: utils.getConstant('DEFAULT_CURRENT_PAGE'),
      pageSize: utils.getConstant('DEFAULT_PAGE_SIZE'),
      
      statusTabs: [
        { label: '全部', value: 'all' },
        { label: '待执行', value: 'pending' },
        { label: '执行中', value: 'executing' },
        { label: '已完成', value: 'completed' },
        { label: '失败', value: 'failed' },
        { label: '已取消', value: 'cancelled' }
      ],
      
      typeTabs: [
        { label: '全部', value: 'all' },
        { label: '定时上架', value: 'on_shelf' },
        { label: '定时下架', value: 'off_shelf' }
      ]
    }
  },
  computed: {
    filteredList() {
      let list = this.taskList
      
      // 状态筛选
      if (this.currentStatus !== 'all') {
        list = list.filter(task => task.status === this.currentStatus)
      }
      
      // 类型筛选
      if (this.currentType !== 'all') {
        list = list.filter(task => task.task_type === this.currentType)
      }
      
      return list
    },
    
    totalStats() {
      const stats = {
        pending: 0,
        completed: 0,
        failed: 0
      }
      
      this.taskList.forEach(task => {
        if (task.status === 'pending') {
          stats.pending++
        } else if (task.status === 'completed') {
          stats.completed++
        } else if (task.status === 'failed') {
          stats.failed++
        }
      })
      
      return stats
    },
    
    emptyStateText() {
      if (this.taskList.length === 0) {
        return '暂无定时任务'
      }
      return '暂无符合条件的任务'
    },
    
    emptyStateDesc() {
      if (this.taskList.length === 0) {
        return '您还没有创建任何定时任务'
      }
      return '请尝试调整筛选条件'
    }
  },
  
  async onShow() {
    this.loadTaskList()
  },

  onReachBottom() {
    this.loadMore()
  },

  methods: {
    async loadTaskList(isLoadMore = false) {
      if (this.loading || (isLoadMore && this.isLoadingMore)) return
      
      if (isLoadMore) {
        this.isLoadingMore = true
      } else {
        this.loading = true
        this.currentPage = utils.getConstant('DEFAULT_CURRENT_PAGE')
      }

      try {
        const requestData = {
          pageIndex: this.currentPage,
          pageSize: this.pageSize
        }

        const result = await callFunction('shelf-management', {
          action: 'getScheduledTasks',
          data: requestData
        })

        if (result.code === 0) {
          const newTasks = result.data.list.map(task => ({
            ...task,
            cancelling: false
          }))

          if (isLoadMore) {
            this.taskList = [...this.taskList, ...newTasks]
          } else {
            this.taskList = newTasks
          }

          // 判断是否还有更多数据
          this.hasMore = newTasks.length === this.pageSize
          if (this.hasMore) {
            this.currentPage++
          }
        } else {
          throw new Error(result.message)
        }

      } catch (error) {
        console.error('加载定时任务列表失败:', error)
        utils.showError('加载失败')
      } finally {
        this.loading = false
        this.isLoadingMore = false
      }
    },

    loadMore() {
      if (this.hasMore && !this.isLoadingMore) {
        this.loadTaskList(true)
      }
    },

    onStatusChange(status) {
      this.currentStatus = status
    },

    onTypeChange(type) {
      this.currentType = type
    },

    // 获取任务标题
    getTaskTitle(task) {
      const action = task.task_type === 'on_shelf' ? '上架' : '下架'
      const target = task.target_type === 'account' ? '账号' : '货架'
      return `定时${action}${target}`
    },

    // 获取任务类型文本
    getTaskTypeText(taskType) {
      return taskType === 'on_shelf' ? '定时上架' : '定时下架'
    },

    // 获取任务类型样式类
    getTaskTypeClass(taskType) {
      return taskType === 'on_shelf' ? 'on-shelf' : 'off-shelf'
    },

    // 获取任务状态文本
    getTaskStatusText(status) {
      const statusMap = {
        pending: '待执行',
        executing: '执行中',
        completed: '已完成',
        failed: '失败',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    },

    // 获取任务状态样式类
    getTaskStatusClass(status) {
      return status
    },

    // 判断是否可以取消任务
    canCancelTask(task) {
      return ['pending', 'failed'].includes(task.status)
    },

    // 取消任务
    async cancelTask(task) {
      try {
        task.cancelling = true
        
        const result = await callFunction('shelf-management', {
          action: 'cancelScheduledTask',
          data: {
            taskId: task._id
          }
        })
        
        if (result.code === 0) {
          utils.showSuccess('任务取消成功')
          task.status = 'cancelled'
        } else {
          utils.showError(result.message)
        }
        
      } catch (error) {
        console.error('取消任务失败:', error)
        utils.showError('取消失败')
      } finally {
        task.cancelling = false
      }
    },

    formatTime(timestamp) {
      if (!timestamp) return '未知'
      return utils.formatTime(timestamp)
    }
  }
}
</script>

<style scoped lang="scss">
// 基础样式复用
.filter-section {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-base;
  box-shadow: $shadow-sm;
}

.filter-tabs {
  display: flex;
  gap: $spacing-sm;
  margin-bottom: $spacing-base;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-tab {
  padding: $spacing-sm $spacing-base;
  border-radius: $border-radius-base;
  font-size: $font-size-sm;
  color: $text-color-secondary;
  background-color: $bg-color-page;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;

  &.active {
    background-color: $primary-color;
    color: white;
  }

  &:hover {
    background-color: rgba(60, 156, 255, 0.1);
    color: $primary-color;

    &.active {
      background-color: $primary-color;
      color: white;
    }
  }
}

.stats {
  display: flex;
  gap: $spacing-sm;
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: $font-size-xxl;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  font-weight: $font-weight-normal;
}

.task-list {
  margin-bottom: $spacing-base;
}

.task-item {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-base;
  box-shadow: $shadow-sm;
  margin-bottom: $spacing-base;
}

.task-header {
  margin-bottom: $spacing-base;
}

.task-info {
  .task-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-color-primary;
    margin-bottom: $spacing-xs;
    display: block;
  }
}

.task-tags {
  display: flex;
  gap: $spacing-xs;
  flex-wrap: wrap;
}

.task-type-tag, .task-status-tag {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;
}

.task-type-tag {
  &.on-shelf {
    background-color: $success-light;
    color: $success-color;
  }

  &.off-shelf {
    background-color: $warning-light;
    color: $warning-color;
  }
}

.task-status-tag {
  &.pending {
    background-color: $info-light;
    color: $info-color;
  }

  &.executing {
    background-color: $primary-light;
    color: $primary-color;
  }

  &.completed {
    background-color: $success-light;
    color: $success-color;
  }

  &.failed {
    background-color: $error-light;
    color: $error-color;
  }

  &.cancelled {
    background-color: $text-color-disabled;
    color: white;
  }
}

.task-content {
  margin-bottom: $spacing-base;
}

.task-detail {
  display: flex;
  margin-bottom: $spacing-xs;

  .detail-label {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    min-width: 120rpx;
    flex-shrink: 0;
  }

  .detail-value {
    font-size: $font-size-sm;
    color: $text-color-primary;
    flex: 1;
    word-break: break-all;
  }
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: $spacing-base;
  border-top: 1rpx solid $border-color-light;
}

.create-time {
  font-size: $font-size-xs;
  color: $text-color-secondary;
}

.task-actions {
  display: flex;
  gap: $spacing-xs;
}

.empty-state {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: $spacing-base;
  display: block;
}

.empty-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  margin-bottom: $spacing-sm;
  display: block;
}

.empty-desc {
  font-size: $font-size-base;
  color: $text-color-secondary;
  line-height: 1.5;
  display: block;
  margin-bottom: $spacing-xl;
}

.load-more {
  text-align: center;
  padding: $spacing-base;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  color: $text-color-secondary;
}
</style>
