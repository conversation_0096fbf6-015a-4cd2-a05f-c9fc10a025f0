<template>
  <view class="action-menu">
    <!-- 操作按钮 -->
    <uv-button
      type="primary"
      size="small"
      :customStyle="buttonStyle"
      @click="showMenu"
      text="操作"
      icon="more-dot-fill"
      iconColor="#fff"
    ></uv-button>

    <!-- 操作菜单弹窗 -->
    <uv-popup
      ref="actionPopup"
      mode="center"
      :customStyle="{
        borderRadius: '24rpx',
        padding: '0',
        width: '500rpx'
      }"
      @close="handleClose"
    >
      <view class="menu-container">
        <view class="menu-header">
          <text class="menu-title">选择操作</text>
          <text class="menu-subtitle">{{ targetName }}</text>
        </view>
        
        <view class="menu-list">
          <view
            v-for="action in availableActions"
            :key="action.key"
            class="menu-item"
            :class="{ disabled: action.disabled }"
            @click="handleAction(action)"
          >
            <view class="menu-item-icon" :class="action.iconClass">
              <text class="menu-icon">{{ action.icon }}</text>
            </view>
            <view class="menu-item-content">
              <text class="menu-item-title">{{ action.title }}</text>
              <text v-if="action.desc" class="menu-item-desc">{{ action.desc }}</text>
            </view>
            <view v-if="action.loading" class="menu-item-loading">
              <uv-loading-icon mode="circle" size="32"></uv-loading-icon>
            </view>
          </view>
        </view>
        
        <view class="menu-footer">
          <uv-button
            type="info"
            plain
            size="small"
            @click="handleClose"
            text="取消"
            :customStyle="{ width: '100%' }"
          ></uv-button>
        </view>
      </view>
    </uv-popup>
  </view>
</template>

<script>
export default {
  name: 'ActionMenu',
  props: {
    // 目标名称（用于显示）
    targetName: {
      type: String,
      default: ''
    },
    // 可用操作列表
    actions: {
      type: Array,
      default: () => []
    },
    // 按钮样式
    buttonStyle: {
      type: Object,
      default: () => ({
        borderRadius: '100px',
        fontSize: '24rpx',
        padding: '0 24rpx'
      })
    }
  },
  computed: {
    availableActions() {
      return this.actions.filter(action => !action.hidden)
    }
  },
  methods: {
    // 显示菜单
    showMenu() {
      this.$refs.actionPopup.open()
    },
    
    // 关闭菜单
    handleClose() {
      this.$refs.actionPopup.close()
    },
    
    // 处理操作点击
    handleAction(action) {
      if (action.disabled || action.loading) {
        return
      }
      
      this.handleClose()
      this.$emit('action', action.key, action)
    }
  }
}
</script>

<style scoped lang="scss">
.action-menu {
  display: inline-block;
}

.menu-container {
  background: $bg-color-container;
  border-radius: 24rpx;
  overflow: hidden;
}

.menu-header {
  padding: $spacing-lg;
  text-align: center;
  border-bottom: 1rpx solid $border-color-light;
  
  .menu-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-color-primary;
    display: block;
    margin-bottom: $spacing-xs;
  }
  
  .menu-subtitle {
    font-size: $font-size-base;
    color: $text-color-secondary;
  }
}

.menu-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: $spacing-base $spacing-lg;
  transition: background-color $transition-fast;
  
  &:not(.disabled):active {
    background-color: $bg-color-overlay;
  }
  
  &.disabled {
    opacity: 0.5;
  }
}

.menu-item-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $spacing-base;
  
  .menu-icon {
    font-size: 32rpx;
    color: white;
  }
  
  &.success {
    background-color: $success-color;
  }
  
  &.warning {
    background-color: $warning-color;
  }
  
  &.primary {
    background-color: $primary-color;
  }
  
  &.error {
    background-color: $error-color;
  }
}

.menu-item-content {
  flex: 1;
  
  .menu-item-title {
    font-size: $font-size-base;
    color: $text-color-primary;
    font-weight: $font-weight-medium;
    display: block;
    margin-bottom: 4rpx;
  }
  
  .menu-item-desc {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    line-height: 1.3;
  }
}

.menu-item-loading {
  margin-left: $spacing-base;
}

.menu-footer {
  padding: $spacing-base $spacing-lg;
  border-top: 1rpx solid $border-color-light;
}
</style>
