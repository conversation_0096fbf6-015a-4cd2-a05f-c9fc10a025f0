/**
 * 认证工具函数
 */

// 需要登录才能访问的页面
const loginPages = [
  '/pages/shelf-monitor/index',
  '/pages/platform-config/index',
  '/pages/platform-config/add',
  '/pages/shelf-list/index',
  '/pages/operation-logs/index'
]

/**
 * 检查用户是否已登录
 */
export function checkLogin() {
  const token = uni.getStorageSync('token')
  const userInfo = uni.getStorageSync('userInfo')
  return !!(token && userInfo)
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return uni.getStorageSync('userInfo')
}

/**
 * 获取token
 */
export function getToken() {
  return uni.getStorageSync('token')
}

/**
 * 清除登录信息
 */
export function clearAuth() {
  uni.removeStorageSync('token')
  uni.removeStorageSync('userInfo')
}

/**
 * 跳转到登录页面
 */
export function toLogin() {
  uni.reLaunch({
    url: '/pages/login/index'
  })
}

/**
 * 登录拦截器
 */
export function loginInterceptor(url) {
  // 检查是否是需要登录的页面
  const needLogin = loginPages.some(page => url.includes(page))
  
  if (needLogin && !checkLogin()) {
    toLogin()
    return false
  }
  
  return true
}

/**
 * 退出登录
 */
export async function logout() {
  try {
    const token = getToken()
    if (token) {
      // 调用云函数退出登录
      await uniCloud.callFunction({
        name: 'user-auth',
        data: {
          action: 'logout'
        }
      })
    }
  } catch (error) {
    console.error('退出登录失败:', error)
  } finally {
    // 清除本地存储
    clearAuth()
    // 跳转到登录页
    toLogin()
  }
}

/**
 * 自动登录检查
 */
export async function autoLogin() {
  const token = getToken()
  if (!token) {
    return false
  }
  
  try {
    // 验证token是否有效
    const result = await uniCloud.callFunction({
      name: 'user-auth',
      data: {
        action: 'getUserInfo'
      }
    })
    
    if (result.result.code === 0) {
      // 更新用户信息
      uni.setStorageSync('userInfo', result.result.data)
      return true
    } else {
      // token无效，清除登录信息
      clearAuth()
      return false
    }
  } catch (error) {
    console.error('自动登录检查失败:', error)
    clearAuth()
    return false
  }
} 