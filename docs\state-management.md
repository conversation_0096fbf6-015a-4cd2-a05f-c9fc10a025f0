# 统一状态管理系统

## 概述

本系统重构了原有的分散状态管理，使用统一的 `unified_state` 字段替代原有的 `status` 和 `rent_status` 字段，实现了跨平台的状态统一管理。

## 状态定义

### 统一状态码

| 状态码 | 状态名称 | 描述 |
|--------|----------|------|
| 0      | 待租     | 货架可以出租 |
| 1      | 出租中   | 货架正在被租用 |
| -1     | 下架     | 货架已下架，不可出租 |
| -2     | 其他状态 | 平台特殊状态或异常状态 |

### 平台状态映射

#### 租号玩 (zuhaowan)
- 原始状态 `zt`：
  - 0 → 统一状态 0 (待租)
  - 1 → 统一状态 1 (出租中)
  - -1 → 统一状态 -1 (下架)
  - 其他 → 统一状态 -2 (其他状态)

#### U号租 (uhaozu)
- 原始状态组合 `goodsStatus` + `rentStatus`：
  - goodsStatus=3 & rentStatus=1 → 统一状态 0 (待租)
  - goodsStatus=3 & rentStatus=0 → 统一状态 1 (出租中)
  - goodsStatus=4 → 统一状态 -1 (下架)
  - 其他组合 → 统一状态 -2 (其他状态)

## 数据库变更

### 原有字段（已删除）
```json
{
  "status": {
    "bsonType": "int",
    "description": "货架状态：0待租，1出租中，2下架，3删除"
  },
  "rent_status": {
    "bsonType": "int", 
    "description": "租赁状态：0空闲，1出租中"
  }
}
```

### 新字段
```json
{
  "unified_state": {
    "bsonType": "int",
    "description": "统一状态：0待租，1出租中，-1下架，-2其他状态",
    "default": 0
  }
}
```

## 代码架构

### 后端状态管理器
- 文件：`uniCloud-aliyun/cloudfunctions/shelf-management/lib/state-manager.js`
- 功能：
  - 状态转换逻辑
  - 状态验证
  - 状态统计
  - 操作权限检查

### 前端状态管理器
- 文件：`common/js/state-manager.js`
- 功能：
  - 状态显示文本
  - 状态样式类
  - 状态过滤
  - 状态统计

### 平台适配器更新
- 租号玩适配器：使用 `StateManager.convertZuhaoWanStatus()`
- U号租适配器：使用 `StateManager.convertUhaoZuStatus()`

## 使用示例

### 后端使用
```javascript
const StateManager = require('./lib/state-manager')

// 状态转换
const unifiedState = StateManager.convertZuhaoWanStatus(ztStatus)

// 状态验证
if (StateManager.isValidState(targetStatus)) {
  // 执行操作
}

// 获取统计信息
const stats = StateManager.getStateStats(shelfList)
```

### 前端使用
```javascript
import { StateManager } from '@/common/js/state-manager.js'

// 获取状态文本
const statusText = StateManager.getStateText(shelf.unified_state)

// 获取状态样式
const statusClass = StateManager.getStateClass(shelf.unified_state)

// 过滤货架
const availableShelves = StateManager.filterShelfsByState(shelfList, 'available')

// 获取操作按钮文本
const buttonText = StateManager.getActionButtonText(shelf.unified_state)
```

## 状态流转

### 正常流转
```
待租(0) ←→ 下架(-1)
   ↓
出租中(1)
```

### 操作权限
- 待租状态：可以下架
- 下架状态：可以上架
- 出租中状态：不可操作（由平台自动管理）
- 其他状态：不可操作

## 前端页面更新

### 货架列表页面 (`pages/shelf-list/index.vue`)
- 使用 `unified_state` 替代 `status` 和 `rent_status`
- 集成 StateManager 进行状态管理
- 更新过滤逻辑和统计逻辑

### 货架监控页面 (`pages/shelf-monitor/index.vue`)
- 更新统计数据计算逻辑
- 使用 StateManager 进行状态统计

## 优势

1. **统一性**：所有平台使用相同的状态定义
2. **可维护性**：状态逻辑集中管理，易于维护
3. **扩展性**：新增平台只需实现状态转换逻辑
4. **一致性**：前后端使用相同的状态管理逻辑
5. **类型安全**：状态码有明确定义，减少错误

## 注意事项

1. 数据迁移：现有数据需要根据原有 `status` 和 `rent_status` 字段计算 `unified_state`
2. 兼容性：确保所有使用状态字段的地方都已更新
3. 测试：需要测试各种状态转换场景
4. 监控：关注状态转换的准确性和性能