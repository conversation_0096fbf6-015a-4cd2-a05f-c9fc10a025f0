<template>
  <uv-popup
    ref="schedulePopup"
    mode="center"
    :customStyle="{
      borderRadius: '24rpx',
      padding: '40rpx',
      width: '600rpx'
    }"
    @close="handleClose"
  >
    <view class="schedule-dialog">
      <view class="dialog-header">
        <text class="dialog-title">{{ dialogTitle }}</text>
        <text class="dialog-subtitle">{{ targetName }}</text>
      </view>
      
      <view class="time-setting">
        <view class="time-row">
          <text class="time-label">天数：</text>
          <uv-number-box
            v-model="form.days"
            :min="0"
            :max="30"
            :customStyle="{ width: '200rpx' }"
          ></uv-number-box>
        </view>
        
        <view class="time-row">
          <text class="time-label">小时：</text>
          <uv-number-box
            v-model="form.hours"
            :min="0"
            :max="23"
            :customStyle="{ width: '200rpx' }"
          ></uv-number-box>
        </view>
        
        <view class="time-row">
          <text class="time-label">分钟：</text>
          <uv-number-box
            v-model="form.minutes"
            :min="0"
            :max="59"
            :customStyle="{ width: '200rpx' }"
          ></uv-number-box>
        </view>
      </view>
      
      <view class="schedule-options" v-if="taskType === 'off_shelf'">
        <view class="option-row">
          <text class="option-label">自动禁用监控：</text>
          <uv-switch
            v-model="form.autoDisableMonitor"
            size="40"
          ></uv-switch>
        </view>
        <text class="option-hint">定时下架时是否自动禁用监控状态，防止意外上架</text>
      </view>
      
      <view class="dialog-actions">
        <uv-button
          type="info"
          plain
          @click="handleCancel"
          :customStyle="{ marginRight: '20rpx' }"
          text="取消"
        ></uv-button>
        <uv-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
          text="确认设置"
        ></uv-button>
      </view>
    </view>
  </uv-popup>
</template>

<script>
import utils from '@/common/js/utils.js'
import { callFunction } from '@/utils/request.js'

export default {
  name: 'ScheduleTaskDialog',
  props: {
    // 任务类型：on_shelf | off_shelf
    taskType: {
      type: String,
      default: 'on_shelf'
    },
    // 目标类型：account | shelf
    targetType: {
      type: String,
      default: 'account'
    },
    // 目标名称（用于显示）
    targetName: {
      type: String,
      default: ''
    },
    // 目标ID
    targetId: {
      type: String,
      default: ''
    },
    // 平台类型（货架需要）
    platformType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      submitting: false,
      form: {
        days: 0,
        hours: 1,
        minutes: 0,
        autoDisableMonitor: true
      }
    }
  },
  computed: {
    dialogTitle() {
      const action = this.taskType === 'on_shelf' ? '上架' : '下架'
      const target = this.targetType === 'account' ? '账号' : '货架'
      return `设置定时${action}${target}`
    }
  },
  methods: {
    // 打开弹窗
    open() {
      this.resetForm()
      this.$refs.schedulePopup.open()
    },

    // 关闭弹窗
    close() {
      this.$refs.schedulePopup.close()
    },

    // 重置表单
    resetForm() {
      this.form = {
        days: 0,
        hours: 1,
        minutes: 0,
        autoDisableMonitor: this.taskType === 'off_shelf'
      }
    },

    // 处理取消
    handleCancel() {
      this.close()
      this.$emit('cancel')
    },

    // 处理关闭
    handleClose() {
      this.$emit('cancel')
    },

    // 处理提交
    async handleSubmit() {
      // 验证时间设置
      const { days, hours, minutes } = this.form
      if (days === 0 && hours === 0 && minutes === 0) {
        utils.showError('请设置执行时间')
        return
      }

      this.submitting = true
      try {
        const taskData = {
          taskType: this.taskType,
          targetType: this.targetType,
          targetId: this.targetId,
          relativeTime: {
            days: days,
            hours: hours,
            minutes: minutes
          },
          autoDisableMonitor: this.form.autoDisableMonitor
        }

        // 如果是货架类型，需要添加平台类型
        if (this.targetType === 'shelf' && this.platformType) {
          taskData.platformType = this.platformType
        }

        const result = await callFunction('shelf-management', {
          action: 'createScheduledTask',
          data: taskData
        })

        if (result.code === 0) {
          utils.showSuccess('定时任务设置成功')
          this.close()
          this.$emit('success')
        } else {
          utils.showError(result.message)
        }

      } catch (error) {
        console.error('设置定时任务失败:', error)
        utils.showError('设置失败')
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.schedule-dialog {
  .dialog-header {
    text-align: center;
    margin-bottom: $spacing-lg;
    
    .dialog-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-bold;
      color: $text-color-primary;
      display: block;
      margin-bottom: $spacing-xs;
    }
    
    .dialog-subtitle {
      font-size: $font-size-base;
      color: $text-color-secondary;
    }
  }
  
  .time-setting {
    margin-bottom: $spacing-lg;
    
    .time-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-base;
      
      .time-label {
        font-size: $font-size-base;
        color: $text-color-primary;
        min-width: 120rpx;
      }
    }
  }
  
  .schedule-options {
    margin-bottom: $spacing-lg;
    padding: $spacing-base;
    background-color: $bg-color-page;
    border-radius: $border-radius-base;
    
    .option-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-xs;
      
      .option-label {
        font-size: $font-size-base;
        color: $text-color-primary;
      }
    }
    
    .option-hint {
      font-size: $font-size-xs;
      color: $text-color-secondary;
      line-height: 1.4;
    }
  }
  
  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-base;
  }
}
</style>
