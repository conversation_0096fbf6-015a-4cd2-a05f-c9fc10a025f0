/**
 * 应用主入口文件
 * {{ AURA-X: Modify - 添加详细注释，改善代码结构. Approval: 寸止(ID:1735374000). }}
 */
import App from './App'
import Vue from 'vue'
import './uni.promisify.adaptor'

// UI组件库
import uvUI from '@/uni_modules/uv-ui-tools'

// 状态管理和工具
import store from '@/store/index.js'
import cache from '@/common/js/minCache.js'
import utils from '@/common/js/utils.js'

// 认证相关
import { loginInterceptor } from '@/utils/auth.js'
// #ifndef VUE3
// {{ AURA-X: Modify - 添加配置说明注释. Approval: 寸止(ID:1735374000). }}
// Vue2 配置
Vue.config.productionTip = false
App.mpType = 'app'

// 全局挂载工具函数
Vue.prototype.$cache = cache
Vue.prototype.$utils = utils
Vue.prototype.$store = store

// 注册UI组件库
Vue.use(uvUI)

// 配置UI组件库默认参数
uni.$uv.setConfig({
  config: {
    unit: 'rpx' // 设置默认单位为rpx
  }
})

// {{ AURA-X: Modify - 优化登录拦截器配置，减少重复代码. Approval: 寸止(ID:1735374000). }}
// 配置路由拦截器，统一处理登录验证
const routeInterceptor = {
  invoke(args) {
    return loginInterceptor(args.url)
  }
}

// 为所有路由跳转方法添加登录拦截
const routeMethods = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
routeMethods.forEach(method => {
  uni.addInterceptor(method, routeInterceptor)
})
// 创建Vue应用实例
const app = new Vue({
  store,
  ...App
})
app.$mount()
// #endif

// #ifdef H5
// H5平台特殊配置（暂无）
// #endif

// #ifdef VUE3
// Vue3 SSR应用创建函数
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  app.use(uvUI)
  return {
    app
  }
}
// #endif
