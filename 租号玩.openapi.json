{"openapi": "3.0.1", "info": {"title": "租号玩", "description": "租号玩官方Api", "version": "1.0.0"}, "tags": [], "paths": {"/api/Login/login": {"post": {"summary": "登录", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"username": {"description": "用户名", "example": "looong", "type": "string"}, "password": {"description": "密码", "example": "5201230.", "type": "string"}, "type": {"description": "1（商户）2（员工）", "example": "2", "type": "string"}}, "required": ["username", "password", "type"]}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "integer"}, "data": {"type": "object", "properties": {"token": {"type": "string"}}, "required": ["token"]}, "message": {"type": "string"}}, "required": ["code", "status", "data", "message"]}, "example": {"code": 200, "status": 200, "data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTI1NDYyNTYsInN1YiI6bnVsbCwibmJmIjoxNzUyNDU5ODU2LCJhdWQiOm51bGwsImlhdCI6MTc1MjQ1OTg1NiwianRpIjoiUDJTQ2oxdGFRTyIsImlzcyI6IkVhc3lTd29vbGUiLCJzdGF0dXMiOjEsImRhdGEiOnsidXNlcm5hbWUiOiJsb29vbmciLCJ0eXBlIjoyLCJpZCI6MTAxMTQzLCJuYW1lIjoi5p2O6b6ZIn19.OZDtIL0-ScEtxhai__6OaZZ1JMlviUbiv4rOLjWBV-U"}, "message": "登录成功"}}}, "headers": {}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/ https://zu.zuhaowan.com/api/Account/search": {"post": {"summary": "获取货架", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "<PERSON><PERSON>", "in": "header", "description": "", "required": true, "example": "___token__=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************.5gZYkzzww_112V0KLSAK1-XXll-hljl0B8VLKr5FIPs", "schema": {"type": "string"}}, {"name": "Token", "in": "header", "description": "", "required": true, "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************.5gZYkzzww_112V0KLSAK1-XXll-hljl0B8VLKr5FIPs", "schema": {"type": "string"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"status": {"description": "空（全部） 0（待租）1（出租中）-1（下架）-2（待审核）-3（审核不通过）-4（删除）", "example": "", "type": "string"}, "pagesize": {"example": "100", "type": "string"}, "pageindex": {"example": "1", "type": "string"}}, "required": ["status", "pagesize", "pageindex"]}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "integer"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "ft": {"type": "string"}, "pn": {"type": "string"}, "gid": {"type": "integer"}, "pid": {"type": "integer"}, "zt": {"type": "integer"}, "c_rank": {"type": "integer"}, "pmoney": {"type": "string"}, "zz_exclusive_pmoney": {"type": "string"}, "bzmoney": {"type": "string"}, "em": {"type": "string"}, "yx": {"type": "string"}, "szq": {"type": "integer"}, "jsm": {"type": "string"}, "zh": {"type": "string"}, "yaoqiu": {"type": "string"}, "claim_status": {"type": "integer"}, "insure_id": {"type": "integer"}, "categoryid": {"type": "integer"}, "game_name": {"type": "string"}, "game_server_name": {"type": "string"}, "game_zone_name": {"type": "string"}, "shfs": {"type": "integer"}, "rank_top": {"type": "integer"}, "timelimit_id": {"type": "integer"}, "soft_show": {"type": "integer"}, "credit_score": {"type": "string"}, "partnerid": {"type": "integer"}, "partnername": {"type": "string"}, "is_quick_login": {"type": "integer"}, "quick_open_style_name": {"type": "string"}, "quick_login_open_style": {"type": "string"}, "h_score": {"type": "integer"}, "hao_game_credit": {"type": "string"}, "show_game_insure": {"type": "integer"}, "show_picc_insure": {"type": "integer"}, "picc": {"type": "object", "properties": {"add_c": {"type": "integer"}, "amount": {"type": "integer"}, "auto_relet": {"type": "integer"}, "claim_amount": {"type": "integer"}, "end_time": {"type": "string"}, "hao_id": {"type": "integer"}, "id": {"type": "integer"}, "insurance_type": {"type": "integer"}, "insure_status": {"type": "integer"}, "new_policy": {"type": "integer"}, "premium": {"type": "integer"}, "start_time": {"type": "string"}, "surplus_amount": {"type": "integer"}}, "required": ["add_c", "amount", "auto_relet", "claim_amount", "end_time", "hao_id", "id", "insurance_type", "insure_status", "new_policy", "premium", "start_time", "surplus_amount"]}, "cpa_status": {"type": "integer"}, "cpa_status_str": {"type": "string"}, "event_guid": {"type": "string"}, "book_id": {"type": "integer"}, "up_time": {"type": "string"}, "book_hours": {"type": "integer"}, "book_minutes": {"type": "integer"}, "book_status": {"type": "integer"}, "is_show_steam_phone_token": {"type": "integer"}, "is_show_r_star_phone_token": {"type": "integer"}, "is_show_battle_gen_token": {"type": "integer"}, "is_show_net_account_maintain": {"type": "integer"}, "quick_client_type": {"type": "string"}, "quick_client_status": {"type": "integer"}, "quick_client_no_expire": {"type": "integer"}, "quick_client_time_message": {"type": "string"}, "quick_client_auth_1574": {"type": "integer"}, "quick_client_auth_1574_status": {"type": "integer"}, "quick_login_switch": {"type": "integer"}, "quick_login_switch_btn": {"type": "integer"}, "quick_wx": {"type": "integer"}, "web_login_status": {"type": "integer"}, "web_login_time": {"type": "string"}, "h_remark": {"type": "string"}, "h_up_pwd_time": {"type": "string"}, "h_tag": {"type": "integer"}, "h_up_pwd_timeout": {"type": "integer"}, "h_up_pwd_hours": {"type": "integer"}, "h_account_region": {"type": "string"}, "speed_flag": {"type": "integer"}, "wwqy_close_zt": {"type": "integer"}, "wwqy_close_model": {"type": "string"}, "wwqy_close_start_time": {"type": "string"}, "wwqy_close_end_time": {"type": "string"}, "no_play": {"type": "integer"}, "allow_no_play": {"type": "integer"}, "allow_no_play_v2": {"type": "integer"}, "hz_game_login_status": {"type": "integer"}, "on_rent_time": {"type": "string"}, "offrent_time": {"type": "string"}, "maybe_face": {"type": "integer"}, "hao_score": {"type": "string"}, "health_time": {"type": "null"}, "haoAlertPwdFlag": {"type": "integer"}, "change_price_status": {"type": "integer"}, "apply": {"type": "integer"}, "ai_price_status": {"type": "integer"}}, "required": ["id", "ft", "pn", "gid", "pid", "zt", "c_rank", "pmoney", "zz_exclusive_pmoney", "bzmoney", "em", "yx", "szq", "jsm", "zh", "<PERSON><PERSON><PERSON><PERSON>", "claim_status", "insure_id", "categoryid", "game_name", "game_server_name", "game_zone_name", "shfs", "rank_top", "timelimit_id", "soft_show", "credit_score", "partnerid", "partnername", "is_quick_login", "quick_open_style_name", "quick_login_open_style", "h_score", "hao_game_credit", "show_game_insure", "show_picc_insure", "picc", "cpa_status", "cpa_status_str", "event_guid", "book_id", "up_time", "book_hours", "book_minutes", "book_status", "is_show_steam_phone_token", "is_show_r_star_phone_token", "is_show_battle_gen_token", "is_show_net_account_maintain", "quick_client_type", "quick_client_status", "quick_client_no_expire", "quick_client_time_message", "quick_client_auth_1574", "quick_client_auth_1574_status", "quick_login_switch", "quick_login_switch_btn", "quick_wx", "web_login_status", "web_login_time", "h_remark", "h_up_pwd_time", "h_tag", "h_up_pwd_timeout", "h_up_pwd_hours", "h_account_region", "speed_flag", "wwqy_close_zt", "wwqy_close_model", "wwqy_close_start_time", "wwqy_close_end_time", "no_play", "allow_no_play", "allow_no_play_v2", "hz_game_login_status", "on_rent_time", "offrent_time", "maybe_face", "hao_score", "health_time", "haoAlertPwdFlag", "change_price_status", "apply", "ai_price_status"]}}, "count": {"type": "integer"}, "account_count": {"type": "object", "properties": {"all": {"type": "integer"}, "deleted": {"type": "integer"}, "hide": {"type": "integer"}, "noPass": {"type": "integer"}, "offRent": {"type": "integer"}, "onRent": {"type": "integer"}, "verify": {"type": "integer"}, "wait": {"type": "integer"}}, "required": ["all", "deleted", "hide", "noPass", "offRent", "onRent", "verify", "wait"]}, "changeSwitch": {"type": "integer"}, "hand_act_info_switch": {"type": "string"}, "hao_score_switch": {"type": "integer"}, "quick_client_v22_pc_open": {"type": "string"}, "sync_role_to_remark_switch": {"type": "string"}, "description": {"type": "null"}, "sublet_price_bat_btn": {"type": "integer"}}, "required": ["list", "count", "account_count", "changeSwitch", "hand_act_info_switch", "hao_score_switch", "quick_client_v22_pc_open", "sync_role_to_remark_switch", "description", "sublet_price_bat_btn"]}, "message": {"type": "string"}}, "required": ["code", "status", "data", "message"]}, "example": {"code": 200, "status": 200, "data": {"list": [{"id": ********, "ft": "2024-09-20 16:21:46", "pn": "3600+皮⭐23神器·满内购丨瀚娜·⑩火麒麟·满级圣诞AK·满女团丨最新装备⭐男女通用", "gid": 581, "pid": 7741, "zt": 0, "c_rank": 115, "pmoney": "9.00", "zz_exclusive_pmoney": "0.00", "bzmoney": "0.00", "em": "0.00", "yx": "", "szq": 1, "jsm": "切勿以身试·法", "zh": "lilonghaoshuai6", "yaoqiu": "", "claim_status": 0, "insure_id": 1846750, "categoryid": 2, "game_name": "绝地求生", "game_server_name": "全服", "game_zone_name": "全区", "shfs": 0, "rank_top": **********, "timelimit_id": 0, "soft_show": 1, "credit_score": "-", "partnerid": 0, "partnername": "", "is_quick_login": 0, "quick_open_style_name": "", "quick_login_open_style": "--", "h_score": -1, "hao_game_credit": "", "show_game_insure": 0, "show_picc_insure": 1, "picc": {"add_c": 50, "amount": 1000, "auto_relet": 2, "claim_amount": 0, "end_time": "2025-07-21 19:14:21", "hao_id": ********, "id": 1224551, "insurance_type": 1, "insure_status": 1, "new_policy": 1, "premium": 30, "start_time": "2025-06-21 19:14:21", "surplus_amount": 1000}, "cpa_status": 3, "cpa_status_str": "已暂停", "event_guid": "97f0926a-4715-42fa-9088-507745adc240", "book_id": 150387, "up_time": "2025-06-28 23:12:16 +0800 CST", "book_hours": 1, "book_minutes": 0, "book_status": 3, "is_show_steam_phone_token": 1, "is_show_r_star_phone_token": 0, "is_show_battle_gen_token": 0, "is_show_net_account_maintain": 0, "quick_client_type": "", "quick_client_status": 0, "quick_client_no_expire": 0, "quick_client_time_message": "", "quick_client_auth_1574": 0, "quick_client_auth_1574_status": 0, "quick_login_switch": 0, "quick_login_switch_btn": 0, "quick_wx": 0, "web_login_status": -2, "web_login_time": "", "h_remark": "", "h_up_pwd_time": "**********", "h_tag": 0, "h_up_pwd_timeout": 0, "h_up_pwd_hours": 0, "h_account_region": "", "speed_flag": 0, "wwqy_close_zt": 0, "wwqy_close_model": "", "wwqy_close_start_time": "", "wwqy_close_end_time": "", "no_play": 0, "allow_no_play": 0, "allow_no_play_v2": 0, "hz_game_login_status": 0, "on_rent_time": "", "offrent_time": "2025-07-13 22:33:58", "maybe_face": 0, "hao_score": "5.0", "health_time": null, "haoAlertPwdFlag": 0, "change_price_status": 0, "apply": 1, "ai_price_status": 0}], "count": 4, "account_count": {"all": 4, "deleted": 2, "hide": 0, "noPass": 0, "offRent": 0, "onRent": 1, "verify": 0, "wait": 3}, "changeSwitch": 1, "hand_act_info_switch": "1", "hao_score_switch": 0, "quick_client_v22_pc_open": "1", "sync_role_to_remark_switch": "1", "description": null, "sublet_price_bat_btn": 0}, "message": ""}}}, "headers": {}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/ https://zu.zuhaowan.com/api/Account/offRent": {"post": {"summary": "下架", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Token", "in": "header", "description": "", "required": true, "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************.PZGEpj6jqUwoG9xaQHhLHilq5A5DjFtZ0oJIrhv1XLA", "schema": {"type": "string"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"id": {"example": "********", "type": "string"}}, "required": ["id"]}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "integer"}, "data": {"type": "object", "properties": {"ys_code": {"type": "null"}}, "required": ["ys_code"]}, "message": {"type": "string"}}, "required": ["code", "status", "data", "message"]}, "example": {"code": 200, "status": 200, "data": {"ys_code": null}, "message": "下架成功！"}}}, "headers": {}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/ https://zu.zuhaowan.com/api/Account/onRent": {"post": {"summary": "上架", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Token", "in": "header", "description": "", "required": true, "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************.PZGEpj6jqUwoG9xaQHhLHilq5A5DjFtZ0oJIrhv1XLA", "schema": {"type": "string"}}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"id": {"example": "********", "type": "string"}}, "required": ["id"]}, "example": ""}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "integer"}, "data": {"type": "object", "properties": {"look_end": {"type": "string"}, "appeal_num": {"type": "string"}, "appeal_status": {"type": "string"}, "ys_code": {"type": "null"}}, "required": ["look_end", "appeal_num", "appeal_status", "ys_code"]}, "message": {"type": "string"}}, "required": ["code", "status", "data", "message"]}, "example": {"code": 200, "status": 200, "data": {"look_end": "", "appeal_num": "", "appeal_status": "", "ys_code": null}, "message": "上架成功！"}}}, "headers": {}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [], "security": []}