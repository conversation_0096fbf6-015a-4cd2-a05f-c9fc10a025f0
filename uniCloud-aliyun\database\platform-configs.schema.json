{"bsonType": "object", "required": ["user_id", "platform_name", "platform_type", "status"], "properties": {"_id": {"description": "平台配置ID"}, "user_id": {"bsonType": "string", "description": "用户ID，关联用户表"}, "platform_name": {"bsonType": "string", "description": "平台名称（如：租号玩、U号租）"}, "platform_type": {"bsonType": "string", "description": "平台类型标识（如：zu<PERSON><PERSON>、uh<PERSON><PERSON>）"}, "username": {"bsonType": "string", "description": "平台登录用户名"}, "password": {"bsonType": "string", "description": "平台登录密码（加密存储）"}, "token": {"bsonType": "string", "description": "平台访问令牌"}, "cookie": {"bsonType": "string", "description": "平台Cookie信息"}, "headers": {"bsonType": "object", "description": "平台请求头信息"}, "auto_login": {"bsonType": "bool", "description": "是否支持自动登录", "default": false}, "login_status": {"bsonType": "int", "description": "登录状态：0未登录，1已登录，2登录失效", "default": 0}, "last_login_time": {"bsonType": "timestamp", "description": "最后登录时间"}, "token_expire_time": {"bsonType": "timestamp", "description": "令牌过期时间"}, "status": {"bsonType": "int", "description": "配置状态：0禁用，1启用", "default": 1}, "remark": {"bsonType": "string", "description": "备注信息"}, "create_time": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}, "update_time": {"bsonType": "timestamp", "description": "更新时间", "forceDefaultValue": {"$env": "now"}}}}